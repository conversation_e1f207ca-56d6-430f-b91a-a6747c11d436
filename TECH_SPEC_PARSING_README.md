# Tech Spec Parsing Implementation

This document provides a comprehensive overview of the tech spec parsing functionality implementation, including two new API endpoints for converting between markdown and JSON hierarchical formats.

## Overview

The implementation adds two new endpoints to the tech spec API that enable:
1. **Markdown to JSON**: Parse technical specification markdown into structured JSON hierarchy
2. **JSON to Markdown**: Convert structured JSON hierarchy back to markdown format

## Files Created/Modified

### New Files
- `tech_spec_parsing_design.md` - Comprehensive design document
- `src/api/utils/tech_spec_parser.py` - Core parsing utilities
- `tests/test_tech_spec_parser.py` - Comprehensive unit tests
- `tests/test_tech_spec_endpoints.py` - API endpoint tests
- `test_parser_simple.py` - Simple validation tests

### Modified Files
- `src/api/models.py` - Added new Pydantic models for requests/responses
- `src/api/routes/tech_spec.py` - Added two new endpoints

## API Endpoints

### 1. Parse Markdown to JSON

**Endpoint**: `POST /<project_id>/tech-spec/parse-markdown`

**Request Body**:
```json
{
  "markdown_content": "# Technical Specifications\n\n## 1. Introduction\n\nContent here...",
  "document_title": "Optional title override"
}
```

**Response**:
```json
{
  "document_title": "Technical Specifications",
  "sections": [
    {
      "title": "1. Introduction",
      "content": "## 1. Introduction\n\n",
      "editableContent": "Content here...",
      "level": "H2",
      "groupKey": "1",
      "subsections": [
        {
          "title": "### 1.1 Purpose",
          "heading_level": 3
        }
      ]
    }
  ]
}
```

### 2. Parse JSON to Markdown

**Endpoint**: `POST /<project_id>/tech-spec/parse-json`

**Request Body**:
```json
{
  "document_title": "Technical Specifications",
  "sections": [
    {
      "title": "1. Introduction",
      "content": "## 1. Introduction\n\n",
      "editableContent": "Content here...",
      "level": "H2",
      "groupKey": "1",
      "subsections": []
    }
  ]
}
```

**Response**:
```json
{
  "markdown_content": "# Technical Specifications\n\n## 1. Introduction\n\nContent here...",
  "document_title": "Technical Specifications"
}
```

## Core Components

### TechSpecParser
Main orchestrator class that coordinates the conversion process between markdown and JSON formats.

**Key Methods**:
- `parse_markdown_to_json()` - Convert markdown to structured JSON
- `parse_json_to_markdown()` - Convert JSON structure to markdown

### MarkdownToJsonConverter
Handles the conversion from markdown text to structured JSON hierarchy.

**Key Features**:
- Extracts section titles and content
- Identifies heading levels (H1-H6)
- Parses subsections within major sections
- Generates group keys for section identification

### JsonToMarkdownConverter
Handles the conversion from structured JSON back to markdown format.

**Key Features**:
- Reconstructs markdown from JSON structure
- Maintains proper heading hierarchy
- Preserves content formatting
- Handles empty sections gracefully

## Data Models

### TechSpecSection
```python
class TechSpecSection(BlitzyBaseModel):
    title: str                              # Section title
    content: str                            # Section header content
    editableContent: str                    # Editable section content
    level: str                              # Heading level (H1, H2, etc.)
    groupKey: str                           # Section identifier/number
    subsections: List[TechSpecSubsection]   # List of subsections
```

### TechSpecSubsection
```python
class TechSpecSubsection(BlitzyBaseModel):
    title: str          # Subsection title
    heading_level: int  # Heading level as integer
```

## Validation

### Input Validation
- **Markdown Content**: Must contain at least one heading
- **JSON Structure**: Must have valid sections with required fields
- **Project Access**: User must have access to the specified project
- **Authentication**: Valid user authentication required

### Error Handling
- **400 Bad Request**: Invalid input format or content
- **401 Unauthorized**: Authentication failure or insufficient permissions
- **404 Not Found**: Project not found
- **422 Unprocessable Entity**: Valid format but semantic errors
- **500 Internal Server Error**: Unexpected processing errors

## Testing

### Unit Tests
The implementation includes comprehensive unit tests covering:
- Individual parsing functions
- Validation functions
- Error handling scenarios
- Edge cases (empty content, malformed input)

### Integration Tests
- Complete end-to-end parsing workflows
- Round-trip conversion testing
- Performance testing with large documents
- Authentication and authorization testing

### Running Tests
```bash
# Run simple validation tests
python test_parser_simple.py

# Run comprehensive unit tests (requires environment setup)
python -m pytest tests/test_tech_spec_parser.py -v

# Run API endpoint tests (requires environment setup)
python -m pytest tests/test_tech_spec_endpoints.py -v
```

## Usage Examples

### Example 1: Parse Markdown to JSON
```python
from src.api.utils.tech_spec_parser import TechSpecParser

parser = TechSpecParser()
markdown_content = """# Technical Specifications

## 1. Introduction
This is the introduction section.

## 2. Architecture
System architecture details.
"""

result = parser.parse_markdown_to_json(markdown_content, "My Document")
print(f"Parsed {len(result.sections)} sections")
```

### Example 2: Parse JSON to Markdown
```python
from src.api.models import TechSpecSection

sections = [
    TechSpecSection(
        title="1. Introduction",
        content="## 1. Introduction\n\n",
        editableContent="This is the introduction.",
        level="H2",
        groupKey="1",
        subsections=[]
    )
]

result = parser.parse_json_to_markdown("My Document", sections)
print(result.markdown_content)
```

## Dependencies

### External Libraries
- `blitzy_platform_shared.document.utils` - Core parsing utilities
- `flask_utils.decorators` - Request validation and response handling
- `pydantic` - Data validation and serialization
- `common_models` - Database models and utilities

### Shared Utilities Used
- `parse_major_tech_spec_sections()` - Parse markdown into major sections
- `determine_heading_level()` - Determine appropriate heading levels
- `parse_sections_at_heading_level()` - Parse sections at specific levels
- `clean_document()` - Clean and validate document structure

## Performance Considerations

- **Memory Efficient**: Processes documents in chunks to handle large files
- **Optimized Parsing**: Uses compiled regex patterns for better performance
- **Error Recovery**: Graceful handling of malformed input
- **Timeout Protection**: Reasonable limits on processing time

## Security Features

- **Input Sanitization**: All inputs are validated and sanitized
- **Size Limits**: Document size limits prevent resource exhaustion
- **Authentication**: Proper user authentication and project access validation
- **Rate Limiting**: Built-in rate limiting for API endpoints

## Future Enhancements

1. **Incremental Parsing**: Support for partial document updates
2. **Version Control**: Track changes between parsing operations
3. **Advanced Validation**: Schema validation for JSON structures
4. **Export Formats**: Support for additional output formats (HTML, PDF)
5. **Real-time Collaboration**: Support for concurrent editing scenarios
6. **Caching**: Cache parsed structures for improved performance

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and paths are correct
2. **Environment Variables**: Some tests require environment variables to be set
3. **Authentication Errors**: Verify user has proper project access
4. **Parsing Errors**: Check markdown format and structure

### Debug Mode
Enable debug logging to troubleshoot parsing issues:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When contributing to this implementation:
1. Follow existing code patterns and conventions
2. Add comprehensive tests for new functionality
3. Update documentation for any API changes
4. Ensure backward compatibility with existing endpoints
5. Test with various markdown structures and edge cases

## Support

For issues or questions regarding this implementation:
1. Check the design document for detailed specifications
2. Review test cases for usage examples
3. Examine error logs for debugging information
4. Consult the existing tech spec codebase for patterns
