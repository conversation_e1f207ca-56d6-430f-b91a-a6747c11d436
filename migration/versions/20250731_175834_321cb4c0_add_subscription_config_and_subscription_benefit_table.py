"""
Auto-generated migration: add_subscription_config_and_subscription_benefit_table
Version: 20250731_175834_321cb4c0
Created At: 2025-07-31T17:58:34.112919
Author: adars<PERSON><PERSON><PERSON><PERSON>har
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        CREATE TABLE IF NOT EXISTS subscription_configs (
            id VARCHAR PRIMARY KEY NOT NULL,
            plan_name VARCHAR(50) NOT NULL,
            quota_code_generation_lines_base INTEGER NOT NULL,
            quota_code_onboarding_lines_base INTEGER NOT NULL,
            charge_lines_generated_overage_base NUMERIC NOT NULL,
            charge_lines_onboarded_overage_base NUMERIC NOT NULL,
            duration_unit VARCHAR(50) NOT NULL,
            description TEXT,
            duration INTEGER NOT NULL,
            is_deleted BOOLEAN NOT NULL DEFAULT False,
            deleted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS subscription_benefits (
            id VARCHAR PRIMARY KEY NOT NULL,
            subscription_config_id VARCHAR,
            charge_lines_generated_overage_delta NUMERIC NOT NULL,
            charge_lines_onboarded_overage_delta NUMERIC NOT NULL,
            quota_code_generation_lines_delta INTEGER NOT NULL,
            quota_code_onboarding_lines_delta INTEGER NOT NULL,
            allotment_type VARCHAR(50) NOT NULL,
            allotted_by VARCHAR,
            subscription_id VARCHAR,
            is_deleted BOOLEAN NOT NULL DEFAULT False,
            deleted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_subscription_benefits_subscription_id FOREIGN KEY (subscription_id) REFERENCES subscriptions(id),
            CONSTRAINT fk_subscription_benefits_subscription_config_id FOREIGN KEY (subscription_config_id) REFERENCES subscription_configs(id)
        );
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN charge_lines_generated_overage NUMERIC;
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN charge_lines_onboarded_overage NUMERIC;
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN quota_code_generation_lines INTEGER;
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN quota_code_onboarding_lines INTEGER;
        """
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE subscriptions DROP COLUMN quota_code_onboarding_lines;",
        "ALTER TABLE subscriptions DROP COLUMN quota_code_generation_lines;",
        "ALTER TABLE subscriptions DROP COLUMN charge_lines_onboarded_overage;",
        "ALTER TABLE subscriptions DROP COLUMN charge_lines_generated_overage;",
        "DROP TABLE subscription_benefits;",
        "DROP TABLE subscription_configs;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
