"""
Test cases for tech spec parsing API endpoints.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from flask import Flask
from src.api.routes.tech_spec import tech_spec_bp
from src.api.models import TechSpecSection, TechSpecSubsection, ParseMarkdownResponse, ParseJsonResponse


@pytest.fixture
def app():
    """Create Flask app for testing."""
    app = Flask(__name__)
    app.register_blueprint(tech_spec_bp, url_prefix='/api')
    app.config['TESTING'] = True
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def sample_markdown_request():
    """Sample markdown request data."""
    return {
        "markdown_content": """# Technical Specifications

## 1. Introduction

This is the introduction section.

### 1.1 Purpose

The purpose of this document.

## 2. Architecture

System architecture details.
""",
        "document_title": "Test Document"
    }


@pytest.fixture
def sample_json_request():
    """Sample JSON request data."""
    return {
        "document_title": "Test Document",
        "sections": [
            {
                "title": "1. Introduction",
                "content": "## 1. Introduction\n\n",
                "editableContent": "This is the introduction section.\n\n### 1.1 Purpose\n\nThe purpose of this document.",
                "level": "H2",
                "groupKey": "1",
                "subsections": [
                    {
                        "title": "### 1.1 Purpose",
                        "heading_level": 3
                    }
                ]
            },
            {
                "title": "2. Architecture",
                "content": "## 2. Architecture\n\n",
                "editableContent": "System architecture details.",
                "level": "H2",
                "groupKey": "2",
                "subsections": []
            }
        ]
    }


@pytest.fixture
def mock_user_info():
    """Mock user info for authentication."""
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>"
    }


class TestParseMarkdownEndpoint:
    """Test cases for parse markdown to JSON endpoint."""
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_markdown_content')
    @patch('src.api.routes.tech_spec.TechSpecParser')
    def test_parse_markdown_success(self, mock_parser_class, mock_validate, mock_db_session, mock_get_user_info, client, sample_markdown_request, mock_user_info):
        """Test successful markdown parsing."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = True
        
        # Mock database session and queries
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_project.id = "test_project_123"
        mock_session.query.return_value.filter.return_value.first.return_value = mock_project
        
        mock_user = MagicMock()
        mock_user.user_id = "test_user_123"
        mock_session.query.return_value.filter.return_value.first.return_value = mock_user
        
        # Mock parser
        mock_parser = MagicMock()
        mock_parser_class.return_value = mock_parser
        
        expected_response = ParseMarkdownResponse(
            document_title="Test Document",
            sections=[
                TechSpecSection(
                    title="1. Introduction",
                    content="## 1. Introduction\n\n",
                    editableContent="This is the introduction section.",
                    level="H2",
                    groupKey="1",
                    subsections=[]
                )
            ]
        )
        mock_parser.parse_markdown_to_json.return_value = expected_response
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 200
        response_data = json.loads(response.data)
        assert response_data['document_title'] == "Test Document"
        assert len(response_data['sections']) == 1
        assert response_data['sections'][0]['title'] == "1. Introduction"
        
        # Verify parser was called correctly
        mock_parser.parse_markdown_to_json.assert_called_once_with(
            markdown_content=sample_markdown_request['markdown_content'],
            document_title=sample_markdown_request['document_title']
        )
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    def test_parse_markdown_project_not_found(self, mock_db_session, mock_get_user_info, client, sample_markdown_request, mock_user_info):
        """Test markdown parsing with non-existent project."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        mock_session.query.return_value.filter.return_value.first.return_value = None  # Project not found
        
        # Make request
        response = client.post(
            '/api/nonexistent_project/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 404
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_markdown_content')
    def test_parse_markdown_invalid_content(self, mock_validate, mock_db_session, mock_get_user_info, client, sample_markdown_request, mock_user_info):
        """Test markdown parsing with invalid content."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = False  # Invalid content
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_project
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 400
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_markdown_content')
    @patch('src.api.routes.tech_spec.TechSpecParser')
    def test_parse_markdown_parser_error(self, mock_parser_class, mock_validate, mock_db_session, mock_get_user_info, client, sample_markdown_request, mock_user_info):
        """Test markdown parsing with parser error."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = True
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_user = MagicMock()
        mock_session.query.return_value.filter.return_value.first.side_effect = [mock_project, mock_user]
        
        # Mock parser to raise exception
        mock_parser = MagicMock()
        mock_parser_class.return_value = mock_parser
        mock_parser.parse_markdown_to_json.side_effect = Exception("Parser error")
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 500


class TestParseJsonEndpoint:
    """Test cases for parse JSON to markdown endpoint."""
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_json_structure')
    @patch('src.api.routes.tech_spec.TechSpecParser')
    def test_parse_json_success(self, mock_parser_class, mock_validate, mock_db_session, mock_get_user_info, client, sample_json_request, mock_user_info):
        """Test successful JSON parsing."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = True
        
        # Mock database session and queries
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_project.id = "test_project_123"
        mock_session.query.return_value.filter.return_value.first.return_value = mock_project
        
        mock_user = MagicMock()
        mock_user.user_id = "test_user_123"
        mock_session.query.return_value.filter.return_value.first.return_value = mock_user
        
        # Mock parser
        mock_parser = MagicMock()
        mock_parser_class.return_value = mock_parser
        
        expected_response = ParseJsonResponse(
            markdown_content="# Test Document\n\n## 1. Introduction\n\nThis is the introduction section.",
            document_title="Test Document"
        )
        mock_parser.parse_json_to_markdown.return_value = expected_response
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-json',
            data=json.dumps(sample_json_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 200
        response_data = json.loads(response.data)
        assert response_data['document_title'] == "Test Document"
        assert "# Test Document" in response_data['markdown_content']
        assert "## 1. Introduction" in response_data['markdown_content']
        
        # Verify parser was called correctly
        mock_parser.parse_json_to_markdown.assert_called_once()
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_json_structure')
    def test_parse_json_invalid_structure(self, mock_validate, mock_db_session, mock_get_user_info, client, sample_json_request, mock_user_info):
        """Test JSON parsing with invalid structure."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = False  # Invalid structure
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_user = MagicMock()
        mock_session.query.return_value.filter.return_value.first.side_effect = [mock_project, mock_user]
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-json',
            data=json.dumps(sample_json_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 400
    
    def test_parse_json_missing_required_fields(self, client):
        """Test JSON parsing with missing required fields."""
        invalid_request = {
            "document_title": "Test Document"
            # Missing sections field
        }
        
        response = client.post(
            '/api/test_project_123/tech-spec/parse-json',
            data=json.dumps(invalid_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 422  # Validation error


class TestAuthenticationAndAuthorization:
    """Test cases for authentication and authorization."""
    
    def test_parse_markdown_no_auth_header(self, client, sample_markdown_request):
        """Test markdown parsing without authentication header."""
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json'
        )
        
        # Should return unauthorized
        assert response.status_code == 401
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    def test_parse_markdown_user_not_found(self, mock_db_session, mock_get_user_info, client, sample_markdown_request, mock_user_info):
        """Test markdown parsing with non-existent user."""
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_session.query.return_value.filter.return_value.first.side_effect = [mock_project, None]  # User not found
        
        # Make request
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(sample_markdown_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Assertions
        assert response.status_code == 401


class TestInputValidation:
    """Test cases for input validation."""
    
    def test_parse_markdown_empty_content(self, client):
        """Test markdown parsing with empty content."""
        empty_request = {
            "markdown_content": "",
            "document_title": "Test Document"
        }
        
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(empty_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Should return validation error
        assert response.status_code in [400, 422]
    
    def test_parse_json_empty_sections(self, client):
        """Test JSON parsing with empty sections."""
        empty_request = {
            "document_title": "Test Document",
            "sections": []
        }
        
        response = client.post(
            '/api/test_project_123/tech-spec/parse-json',
            data=json.dumps(empty_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Should return validation error
        assert response.status_code in [400, 422]
    
    def test_parse_markdown_invalid_json(self, client):
        """Test markdown parsing with invalid JSON."""
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data="invalid json",
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Should return bad request
        assert response.status_code == 400
    
    def test_parse_json_malformed_sections(self, client):
        """Test JSON parsing with malformed sections."""
        malformed_request = {
            "document_title": "Test Document",
            "sections": [
                {
                    "title": "Section 1",
                    # Missing required fields
                }
            ]
        }
        
        response = client.post(
            '/api/test_project_123/tech-spec/parse-json',
            data=json.dumps(malformed_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Should return validation error
        assert response.status_code == 422


class TestPerformanceAndLimits:
    """Test cases for performance and limits."""
    
    @patch('src.api.routes.tech_spec.get_user_info')
    @patch('src.api.routes.tech_spec.get_db_session')
    @patch('src.api.routes.tech_spec.validate_markdown_content')
    def test_parse_large_markdown_document(self, mock_validate, mock_db_session, mock_get_user_info, client, mock_user_info):
        """Test parsing of large markdown document."""
        # Create a large markdown document
        large_content = "# Large Document\n\n" + "## Section\n\nContent here.\n\n" * 1000
        
        large_request = {
            "markdown_content": large_content,
            "document_title": "Large Document"
        }
        
        # Setup mocks
        mock_get_user_info.return_value = mock_user_info
        mock_validate.return_value = True
        
        mock_session = MagicMock()
        mock_db_session.return_value.__enter__.return_value = mock_session
        
        mock_project = MagicMock()
        mock_user = MagicMock()
        mock_session.query.return_value.filter.return_value.first.side_effect = [mock_project, mock_user]
        
        # Make request (this should handle large content gracefully)
        response = client.post(
            '/api/test_project_123/tech-spec/parse-markdown',
            data=json.dumps(large_request),
            content_type='application/json',
            headers={'Authorization': 'Bearer test_token'}
        )
        
        # Should not timeout or crash
        assert response.status_code in [200, 400, 500]  # Any valid HTTP response
