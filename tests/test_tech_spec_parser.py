"""
Test cases for tech spec parsing functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from src.api.utils.tech_spec_parser import (
    TechSpecParser, MarkdownToJsonConverter, JsonToMarkdownConverter,
    validate_markdown_content, validate_json_structure
)
from src.api.models import TechSpecSection, TechSpecSubsection, ParseMarkdownResponse, ParseJsonResponse


class TestTechSpecParser:
    """Test cases for TechSpecParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = TechSpecParser()
        self.sample_markdown = """# Technical Specifications

## 1. Introduction

This is the introduction section with some content.

### 1.1 Purpose

The purpose of this document is to outline the technical specifications.

## 2. Architecture

This section describes the system architecture.

### 2.1 Components

List of system components:
- Component A
- Component B

```python
def example_function():
    return "Hello World"
```

## 3. Implementation

Implementation details go here.
"""
        
        self.sample_sections = [
            TechSpecSection(
                title="1. Introduction",
                content="## 1. Introduction\n\n",
                editableContent="This is the introduction section with some content.\n\n### 1.1 Purpose\n\nThe purpose of this document is to outline the technical specifications.",
                level="H2",
                groupKey="1",
                subsections=[
                    TechSpecSubsection(title="### 1.1 Purpose", heading_level=3)
                ]
            ),
            TechSpecSection(
                title="2. Architecture",
                content="## 2. Architecture\n\n",
                editableContent="This section describes the system architecture.\n\n### 2.1 Components\n\nList of system components:\n- Component A\n- Component B\n\n```python\ndef example_function():\n    return \"Hello World\"\n```",
                level="H2",
                groupKey="2",
                subsections=[
                    TechSpecSubsection(title="### 2.1 Components", heading_level=3)
                ]
            ),
            TechSpecSection(
                title="3. Implementation",
                content="## 3. Implementation\n\n",
                editableContent="Implementation details go here.",
                level="H2",
                groupKey="3",
                subsections=[]
            )
        ]
    
    @patch('src.api.utils.tech_spec_parser.clean_document')
    @patch('src.api.utils.tech_spec_parser.parse_major_tech_spec_sections')
    def test_parse_markdown_to_json_success(self, mock_parse_sections, mock_clean_document):
        """Test successful markdown to JSON parsing."""
        # Mock the dependencies
        mock_clean_document.return_value = self.sample_markdown
        mock_parse_sections.return_value = {
            "1. Introduction": "## 1. Introduction\n\nThis is the introduction section with some content.\n\n### 1.1 Purpose\n\nThe purpose of this document is to outline the technical specifications.",
            "2. Architecture": "## 2. Architecture\n\nThis section describes the system architecture.\n\n### 2.1 Components\n\nList of system components:\n- Component A\n- Component B\n\n```python\ndef example_function():\n    return \"Hello World\"\n```",
            "3. Implementation": "## 3. Implementation\n\nImplementation details go here."
        }
        
        # Execute the test
        result = self.parser.parse_markdown_to_json(self.sample_markdown, "Test Document")
        
        # Assertions
        assert isinstance(result, ParseMarkdownResponse)
        assert result.document_title == "Test Document"
        assert len(result.sections) == 3
        assert result.sections[0].title == "1. Introduction"
        assert result.sections[0].level == "H2"
        assert result.sections[0].groupKey == "1"
        
        # Verify mocks were called
        mock_clean_document.assert_called_once_with(self.sample_markdown)
        mock_parse_sections.assert_called_once()
    
    def test_parse_json_to_markdown_success(self):
        """Test successful JSON to markdown parsing."""
        # Execute the test
        result = self.parser.parse_json_to_markdown("Test Document", self.sample_sections)
        
        # Assertions
        assert isinstance(result, ParseJsonResponse)
        assert result.document_title == "Test Document"
        assert "# Test Document" in result.markdown_content
        assert "## 1. Introduction" in result.markdown_content
        assert "## 2. Architecture" in result.markdown_content
        assert "## 3. Implementation" in result.markdown_content
    
    def test_extract_document_title_from_content(self):
        """Test document title extraction from markdown content."""
        markdown_with_title = "# My Custom Title\n\n## Section 1\n\nContent here."
        result = self.parser._extract_document_title(markdown_with_title)
        assert result == "My Custom Title"
    
    def test_extract_document_title_default(self):
        """Test default document title when none found."""
        markdown_without_title = "## Section 1\n\nContent here."
        result = self.parser._extract_document_title(markdown_without_title)
        assert result == "Technical Specifications"


class TestMarkdownToJsonConverter:
    """Test cases for MarkdownToJsonConverter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = MarkdownToJsonConverter()
    
    def test_extract_group_key_with_number(self):
        """Test group key extraction from numbered titles."""
        result = self.converter._extract_group_key("1. Introduction", 1)
        assert result == "1"
        
        result = self.converter._extract_group_key("2.5 Advanced Topics", 2)
        assert result == "2"
    
    def test_extract_group_key_without_number(self):
        """Test group key extraction when no number present."""
        result = self.converter._extract_group_key("Introduction", 3)
        assert result == "3"
    
    def test_determine_section_level(self):
        """Test heading level determination."""
        assert self.converter._determine_section_level("# Title") == "H1"
        assert self.converter._determine_section_level("## Title") == "H2"
        assert self.converter._determine_section_level("### Title") == "H3"
        assert self.converter._determine_section_level("#### Title") == "H4"
        assert self.converter._determine_section_level("##### Title") == "H5"
        assert self.converter._determine_section_level("###### Title") == "H6"
        assert self.converter._determine_section_level("Title") == "H1"  # Default
    
    def test_split_section_content(self):
        """Test section content splitting."""
        title = "## 1. Introduction"
        content = "## 1. Introduction\n\nThis is the content of the introduction section."
        
        header_content, editable_content = self.converter._split_section_content(title, content)
        
        assert header_content == "## 1. Introduction\n\n"
        assert editable_content == "This is the content of the introduction section."
    
    def test_parse_subsections(self):
        """Test subsection parsing."""
        content = """## 1. Main Section

This is main content.

### 1.1 Subsection One

Content for subsection one.

### 1.2 Subsection Two

Content for subsection two.

#### 1.2.1 Sub-subsection

This should not be included as it's level 4.
"""
        
        subsections = self.converter._parse_subsections(content)
        
        assert len(subsections) == 2
        assert subsections[0].title == "### 1.1 Subsection One"
        assert subsections[0].heading_level == 3
        assert subsections[1].title == "### 1.2 Subsection Two"
        assert subsections[1].heading_level == 3


class TestJsonToMarkdownConverter:
    """Test cases for JsonToMarkdownConverter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = JsonToMarkdownConverter()
        self.sample_section = TechSpecSection(
            title="1. Introduction",
            content="## 1. Introduction\n\n",
            editableContent="This is the introduction section.",
            level="H2",
            groupKey="1",
            subsections=[]
        )
    
    def test_convert_section(self):
        """Test single section conversion."""
        result = self.converter._convert_section(self.sample_section)
        
        expected = "## 1. Introduction\n\nThis is the introduction section.\n"
        assert result == expected
    
    def test_convert_section_empty_editable_content(self):
        """Test section conversion with empty editable content."""
        section = TechSpecSection(
            title="1. Introduction",
            content="## 1. Introduction\n\n",
            editableContent="",
            level="H2",
            groupKey="1",
            subsections=[]
        )
        
        result = self.converter._convert_section(section)
        expected = "## 1. Introduction\n\n"
        assert result == expected
    
    def test_convert_full_document(self):
        """Test full document conversion."""
        sections = [self.sample_section]
        result = self.converter.convert("Test Document", sections)
        
        assert result.startswith("# Test Document\n")
        assert "## 1. Introduction" in result
        assert "This is the introduction section." in result


class TestValidationFunctions:
    """Test cases for validation functions."""
    
    def test_validate_markdown_content_valid(self):
        """Test markdown content validation with valid input."""
        valid_markdown = "# Title\n\n## Section\n\nContent here."
        assert validate_markdown_content(valid_markdown) is True
    
    def test_validate_markdown_content_invalid_empty(self):
        """Test markdown content validation with empty input."""
        assert validate_markdown_content("") is False
        assert validate_markdown_content("   ") is False
        assert validate_markdown_content(None) is False
    
    def test_validate_markdown_content_invalid_no_headings(self):
        """Test markdown content validation without headings."""
        no_headings = "Just some text without any headings."
        assert validate_markdown_content(no_headings) is False
    
    def test_validate_json_structure_valid(self):
        """Test JSON structure validation with valid input."""
        valid_sections = [
            TechSpecSection(
                title="Section 1",
                content="Content",
                editableContent="Editable",
                level="H2",
                groupKey="1",
                subsections=[]
            )
        ]
        assert validate_json_structure(valid_sections) is True
    
    def test_validate_json_structure_invalid_empty(self):
        """Test JSON structure validation with empty input."""
        assert validate_json_structure([]) is False
    
    def test_validate_json_structure_invalid_missing_fields(self):
        """Test JSON structure validation with missing required fields."""
        invalid_sections = [
            TechSpecSection(
                title="",  # Empty title
                content="Content",
                editableContent="Editable",
                level="H2",
                groupKey="1",
                subsections=[]
            )
        ]
        assert validate_json_structure(invalid_sections) is False


class TestErrorHandling:
    """Test cases for error handling scenarios."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = TechSpecParser()
    
    @patch('src.api.utils.tech_spec_parser.clean_document')
    def test_parse_markdown_to_json_error_handling(self, mock_clean_document):
        """Test error handling in markdown to JSON parsing."""
        # Mock clean_document to raise an exception
        mock_clean_document.side_effect = Exception("Parsing error")
        
        with pytest.raises(Exception) as exc_info:
            self.parser.parse_markdown_to_json("invalid markdown")
        
        assert "Parsing error" in str(exc_info.value)
    
    def test_parse_json_to_markdown_error_handling(self):
        """Test error handling in JSON to markdown parsing."""
        # Create invalid sections that might cause errors
        invalid_sections = [
            TechSpecSection(
                title=None,  # This might cause issues
                content="Content",
                editableContent="Editable",
                level="H2",
                groupKey="1",
                subsections=[]
            )
        ]
        
        with pytest.raises(Exception):
            self.parser.parse_json_to_markdown("Test", invalid_sections)


# Integration test data
SAMPLE_REAL_MARKDOWN = """# Technical Specifications

## 1. Project Overview

This document outlines the technical specifications for the new web application.

### 1.1 Purpose

The purpose is to create a scalable web application.

### 1.2 Scope

The scope includes frontend and backend development.

## 2. System Architecture

The system follows a microservices architecture.

### 2.1 Frontend

React-based single page application.

### 2.2 Backend

Node.js with Express framework.

```javascript
const express = require('express');
const app = express();

app.get('/', (req, res) => {
    res.send('Hello World!');
});
```

## 3. Database Design

PostgreSQL database with the following tables:

- Users
- Projects
- Tasks

## 4. API Endpoints

RESTful API with the following endpoints:

- GET /api/users
- POST /api/projects
- PUT /api/tasks/:id
"""


class TestIntegration:
    """Integration tests for the complete parsing workflow."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = TechSpecParser()
    
    @patch('src.api.utils.tech_spec_parser.clean_document')
    @patch('src.api.utils.tech_spec_parser.parse_major_tech_spec_sections')
    def test_full_round_trip_conversion(self, mock_parse_sections, mock_clean_document):
        """Test complete round-trip conversion: markdown -> JSON -> markdown."""
        # Mock the dependencies for consistent testing
        mock_clean_document.side_effect = lambda x: x  # Return input unchanged
        mock_parse_sections.return_value = {
            "1. Project Overview": "## 1. Project Overview\n\nThis document outlines the technical specifications for the new web application.\n\n### 1.1 Purpose\n\nThe purpose is to create a scalable web application.\n\n### 1.2 Scope\n\nThe scope includes frontend and backend development.",
            "2. System Architecture": "## 2. System Architecture\n\nThe system follows a microservices architecture.\n\n### 2.1 Frontend\n\nReact-based single page application.\n\n### 2.2 Backend\n\nNode.js with Express framework.\n\n```javascript\nconst express = require('express');\nconst app = express();\n\napp.get('/', (req, res) => {\n    res.send('Hello World!');\n});\n```",
            "3. Database Design": "## 3. Database Design\n\nPostgreSQL database with the following tables:\n\n- Users\n- Projects\n- Tasks",
            "4. API Endpoints": "## 4. API Endpoints\n\nRESTful API with the following endpoints:\n\n- GET /api/users\n- POST /api/projects\n- PUT /api/tasks/:id"
        }
        
        # Step 1: Parse markdown to JSON
        json_result = self.parser.parse_markdown_to_json(SAMPLE_REAL_MARKDOWN, "Test Document")
        
        # Verify JSON structure
        assert isinstance(json_result, ParseMarkdownResponse)
        assert json_result.document_title == "Test Document"
        assert len(json_result.sections) == 4
        
        # Step 2: Parse JSON back to markdown
        markdown_result = self.parser.parse_json_to_markdown(
            json_result.document_title,
            json_result.sections
        )
        
        # Verify markdown structure
        assert isinstance(markdown_result, ParseJsonResponse)
        assert "# Test Document" in markdown_result.markdown_content
        assert "## 1. Project Overview" in markdown_result.markdown_content
        assert "## 2. System Architecture" in markdown_result.markdown_content
        assert "## 3. Database Design" in markdown_result.markdown_content
        assert "## 4. API Endpoints" in markdown_result.markdown_content
