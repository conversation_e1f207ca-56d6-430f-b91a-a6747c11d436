import json
import os
from typing import Any

from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.consts import DEFAULT_NAME
from blitzy_utils.logger import logger
from google.cloud.exceptions import Conflict, Forbidden, NotFound, GoogleCloudError
from google.cloud.storage import Blob
from google.cloud.storage.retry import DEFAULT_RETRY

from src.consts import storage_client, GCS_BUCKET_NAME
from src.error.errors import ConflictingResourceError, InvalidRequest, ResourceNotFound
from src.service.company_service import get_company_by_id

UPLOAD_TIMEOUT = 600


def load_cors_config():
    """Load CORS configuration from cors_config.json file"""
    try:
        # Get the path to cors_config.json relative to the project root
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        cors_config_path = os.path.join(project_root, 'cors_config.json')

        with open(cors_config_path, 'r') as f:
            cors_config = json.load(f)

        logger.info(f"Loaded CORS configuration from {cors_config_path}")
        return cors_config
    except Exception as e:
        logger.error(f"Error loading CORS configuration: {e}")
        raise


def create_bucket(bucket_name, location='US'):
    cors_configuration = load_cors_config()
    try:
        # Create the bucket
        bucket = storage_client.bucket(bucket_name)
        bucket.location = location
        bucket = storage_client.create_bucket(bucket)

        bucket.cors = cors_configuration
        bucket.patch()

        logger.info(f"Bucket {bucket.name} created successfully in {bucket.location} with CORS config")
        return bucket

    except Conflict:
        logger.warning(f"Bucket {bucket_name} already exists")
        raise ConflictingResourceError(f"Bucket {bucket_name} already exists")
    except Exception as e:
        logger.error(f"Error creating bucket: {e}")
        return None


def bucket_exists(bucket_name):
    try:
        storage_client.get_bucket(bucket_name)
        return True
    except NotFound:
        return False
    except Forbidden:
        logger.warning(f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
        raise ConflictingResourceError(
            f"Access denied to bucket {bucket_name}. It may exist but you don't have permission.")
    except Exception as e:
        logger.error(f"Error checking bucket: {e}")
        raise e


def get_company_bucket_name(company_id):
    if not company_id or company_id == DEFAULT_NAME:
        return GCS_BUCKET_NAME
    company_info = get_company_by_id(company_id)
    if not company_info:
        raise InvalidRequest(f"Company with id {company_id} not found")
    return f"{GCS_BUCKET_NAME}-{company_id}"


def copy_file_from_default_bucket(file_path: str, source_bucket: str, dest_bucket: str):
    logger.info(f"Copying file {file_path} from default bucket to company bucket")
    source_blob = file_exists(source_bucket, file_path)
    if not source_blob:
        logger.warning(f"File {file_path} not found in default bucket {source_bucket}")
        raise InvalidRequest(f"File {file_path} not found in bucket {source_bucket}")

    create_bucket_if_not_exists(dest_bucket)
    logger.info(f"File {file_path} found in default bucket {source_bucket}. Attempting copy.")
    dest_bucket_obj = storage_client.bucket(dest_bucket)
    dest_blob = dest_bucket_obj.copy_blob(source_blob, dest_bucket_obj, file_path)
    logger.info(f"Successfully copied '{file_path}' from '{source_bucket}' to '{dest_bucket}'")
    return dest_blob


def copy_folder_from_default_bucket(prefix: str, source_bucket: str, dest_bucket: str):
    """
    Copy all files from a folder (prefix) in the default bucket to the company bucket
    """
    logger.info(f"Copying folder with prefix '{prefix}' from {source_bucket} to {dest_bucket}")

    source_bucket_obj = storage_client.bucket(source_bucket)
    dest_bucket_obj = storage_client.bucket(dest_bucket)

    # List all blobs with the given prefix
    source_blobs = list(source_bucket_obj.list_blobs(prefix=prefix))

    if not source_blobs:
        logger.warning(f"No files found with prefix '{prefix}' in source bucket {source_bucket}")
        return

    copied_count = 0
    for source_blob in source_blobs:
        try:
            # Copy each blob to the destination bucket with the same name
            dest_blob = dest_bucket_obj.copy_blob(source_blob, dest_bucket_obj, source_blob.name)
            copied_count += 1
            logger.debug(f"Copied: {source_blob.name}")
        except Exception as e:
            logger.error(f"Failed to copy {source_blob.name}: {str(e)}")
            # Continue copying other files even if one fails
            continue

    logger.info(
        f"Successfully copied {copied_count} files from folder '{prefix}' from '{source_bucket}' to '{dest_bucket}'")


@blitzy_exponential_retry()
def gcs_upload_string(bucket_name, file_path, data, content_type=None):
    """Upload a string to a GCS bucket"""
    logger.info(f"Uploading string to bucket {bucket_name} with path {file_path}")
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(file_path)
    modified_retry = DEFAULT_RETRY.with_timeout(UPLOAD_TIMEOUT).with_delay(initial=1.5, multiplier=1.2, maximum=45.0)
    blob.upload_from_string(data, content_type=content_type, retry=modified_retry)
    logger.info(f"String uploaded successfully to bucket {bucket_name} with path {file_path}")


@blitzy_exponential_retry()
def create_bucket_if_not_exists(bucket_name):
    logger.info(f"Checking if bucket {bucket_name} exists")
    is_exists = bucket_exists(bucket_name)
    if not is_exists:
        logger.info(f"Bucket {bucket_name} does not exist. Creating")
        create_bucket(bucket_name)
        logger.info(f"Bucket {bucket_name} created successfully")
        return

    logger.info(f"Bucket {bucket_name} exists.")


def file_exists(bucket_name: str, file_path: str) -> Any:
    """
    Checks the existence of a file (blob) in a Google Cloud Storage bucket.

    :param bucket_name: Name of the Google Cloud Storage bucket.
    :param file_path: Path of the file (blob) to check within the bucket.
    :return: True if the file exists, False otherwise.
    :param client: Storage client object.
    :rtype: Blob object or None if the file does not exist.
    """
    try:
        # Get bucket and blob
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(file_path)

        # Check if the blob exists
        exists = blob.exists(retry=DEFAULT_RETRY)

        if exists:
            return blob

    except NotFound as e:
        logger.error(f"Bucket '{bucket_name}' not found: {e}")
        return None

    except GoogleCloudError as e:
        logger.error(f"Google Cloud error occurred: {e}")
        return None

    except Exception as e:
        logger.error(f"Unexpected error occurred while checking file existence: {e}")
        raise None

    return None


def copy_file(source_bucket, source_path, dest_bucket, dest_path) -> Blob:
    logger.info(f"Received request to copy file from {source_bucket}/{source_path} to {dest_bucket}/{dest_path}")
    source_blob = file_exists(source_bucket, source_path)
    if not source_blob:
        message = f"File {source_path} not found in bucket {source_bucket}"
        logger.warning(message)
        raise ResourceNotFound(message)

    create_bucket_if_not_exists(dest_bucket)

    dest_bucket_obj = storage_client.bucket(dest_bucket)
    dest_blob = dest_bucket_obj.copy_blob(source_blob, dest_bucket_obj, dest_path, retry=DEFAULT_RETRY)
    logger.info(f"Successfully copied.")
    return dest_blob
