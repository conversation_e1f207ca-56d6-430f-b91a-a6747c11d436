"""
Tech Spec Parsing Utilities

This module provides utilities for parsing technical specifications between
markdown and JSON hierarchical formats.
"""

import re
from typing import Dict, List, Optional, Tuple

from blitzy_utils.logger import logger
from blitzy_platform_shared.document.utils import (
    parse_major_tech_spec_sections,
    determine_heading_level,
    parse_sections_at_heading_level,
    clean_document
)

from src.api.models import TechSpecSection, TechSpecSubsection, ParseMarkdownResponse, ParseJsonResponse


class TechSpecParser:
    """Main class for parsing technical specifications between markdown and JSON formats."""
    
    def __init__(self):
        self.markdown_to_json_converter = MarkdownToJsonConverter()
        self.json_to_markdown_converter = JsonToMarkdownConverter()
    
    def parse_markdown_to_json(self, markdown_content: str, document_title: Optional[str] = None) -> ParseMarkdownResponse:
        """
        Parse markdown content to JSON hierarchy.
        
        Args:
            markdown_content: Raw markdown content
            document_title: Optional document title override
            
        Returns:
            ParseMarkdownResponse with structured sections
        """
        try:
            logger.info("Starting markdown to JSON parsing")
            
            # Clean and normalize the document
            cleaned_content = clean_document(markdown_content)
            
            # Extract document title if not provided
            if not document_title:
                document_title = self._extract_document_title(cleaned_content)
            
            # Parse sections
            sections = self.markdown_to_json_converter.convert(cleaned_content)
            
            logger.info(f"Successfully parsed {len(sections)} sections")
            return ParseMarkdownResponse(
                document_title=document_title,
                sections=sections
            )
            
        except Exception as e:
            logger.error(f"Error parsing markdown to JSON: {str(e)}")
            raise
    
    def parse_json_to_markdown(self, document_title: str, sections: List[TechSpecSection]) -> str:
        """
        Parse JSON hierarchy to markdown content.

        Args:
            document_title: Document title
            sections: List of structured sections

        Returns:
            Generated markdown content as string
        """
        try:
            logger.info(f"Starting JSON to markdown parsing for {len(sections)} sections")

            # Convert sections to markdown
            markdown_content = self.json_to_markdown_converter.convert(document_title, sections)

            # Clean the final document
            cleaned_markdown = clean_document(markdown_content)

            logger.info("Successfully converted JSON to markdown")
            return cleaned_markdown

        except Exception as e:
            logger.error(f"Error parsing JSON to markdown: {str(e)}")
            raise
    
    def _extract_document_title(self, content: str) -> str:
        """Extract document title from markdown content."""
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# ') and line != '# Technical Specification':
                return line[2:].strip()
        return 'Technical Specifications'


class MarkdownToJsonConverter:
    """Converts markdown content to JSON hierarchy."""
    
    def convert(self, markdown_content: str) -> List[TechSpecSection]:
        """Convert markdown to structured sections."""
        try:
            # Parse major sections
            major_sections = parse_major_tech_spec_sections(markdown_content)
            
            sections = []
            for i, (section_title, section_content) in enumerate(major_sections.items(), 1):
                # Extract section number from title
                group_key = self._extract_group_key(section_title, i)
                
                # Determine heading level
                level = self._determine_section_level(section_title)
                
                # Split content into header and editable content
                header_content, editable_content = self._split_section_content(section_title, section_content)
                
                # Parse subsections
                subsections = self._parse_subsections(section_content)
                
                section = TechSpecSection(
                    title=section_title,
                    content=header_content,
                    editableContent=editable_content,
                    level=level,
                    groupKey=group_key,
                    subsections=subsections
                )
                sections.append(section)
            
            return sections
            
        except Exception as e:
            logger.error(f"Error in markdown to JSON conversion: {str(e)}")
            raise
    
    def _extract_group_key(self, title: str, index: int) -> str:
        """Extract group key from section title."""
        # Try to extract number from title like "1. Introduction"
        match = re.match(r'^(\d+)\.?\s*', title)
        if match:
            return match.group(1)
        return str(index)
    
    def _determine_section_level(self, title: str) -> str:
        """Determine heading level from title."""
        if title.startswith('# '):
            return 'H1'
        elif title.startswith('## '):
            return 'H2'
        elif title.startswith('### '):
            return 'H3'
        elif title.startswith('#### '):
            return 'H4'
        elif title.startswith('##### '):
            return 'H5'
        elif title.startswith('###### '):
            return 'H6'
        return 'H1'  # Default
    
    def _split_section_content(self, title: str, content: str) -> Tuple[str, str]:
        """Split section content into header and editable parts."""
        # The header content is just the title with proper formatting
        header_content = f"{title}\n\n"
        
        # Remove the title from the content to get editable content
        lines = content.split('\n')
        editable_lines = []
        skip_first_title = True
        
        for line in lines:
            if skip_first_title and line.strip() == title.strip():
                skip_first_title = False
                continue
            editable_lines.append(line)
        
        editable_content = '\n'.join(editable_lines).strip()
        return header_content, editable_content
    
    def _parse_subsections(self, content: str) -> List[TechSpecSubsection]:
        """Parse subsections from section content."""
        subsections = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('##') and not line.startswith('###'):
                # This is a level 2 heading (subsection)
                heading_level = len(line.split()[0])  # Count # characters
                title = line
                subsections.append(TechSpecSubsection(
                    title=title,
                    heading_level=heading_level
                ))
        
        return subsections


class JsonToMarkdownConverter:
    """Converts JSON hierarchy to markdown content."""
    
    def convert(self, document_title: str, sections: List[TechSpecSection]) -> str:
        """Convert structured sections to markdown."""
        try:
            markdown_parts = []
            
            # Add document title
            markdown_parts.append(f"# {document_title}\n")
            
            # Convert each section
            for section in sections:
                section_markdown = self._convert_section(section)
                markdown_parts.append(section_markdown)
            
            return '\n'.join(markdown_parts)
            
        except Exception as e:
            logger.error(f"Error in JSON to markdown conversion: {str(e)}")
            raise
    
    def _convert_section(self, section: TechSpecSection) -> str:
        """Convert a single section to markdown."""
        parts = []
        
        # Add section header
        parts.append(section.content.rstrip())
        
        # Add editable content
        if section.editableContent.strip():
            parts.append(section.editableContent.rstrip())
        
        return '\n'.join(parts) + '\n'


# Utility functions for validation and error handling
def validate_markdown_content(content: str) -> bool:
    """Validate markdown content for basic structure."""
    if not content or not content.strip():
        return False
    
    # Check for basic markdown structure
    lines = content.split('\n')
    has_heading = any(line.strip().startswith('#') for line in lines)
    
    return has_heading


def validate_json_structure(sections: List[TechSpecSection]) -> bool:
    """Validate JSON structure for required fields."""
    if not sections:
        return False
    
    for section in sections:
        if not section.title or not section.level or not section.groupKey:
            return False
    
    return True
