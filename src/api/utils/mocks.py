"""
Mock implementations for API endpoints.
This module provides mock data generators for various API models.
"""
import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Optional
import random
from blitzy_utils.logger import logger
from pydantic import EmailStr

# Import all required models and enums from src.api.models
from src.api.models import (
    TeamRole,
    ActivityType,
    EntityType,
    IntegrationType,
    CreateTeamInput,
    UpdateTeamInput,
    TeamMemberModel,
    TeamModel,
    TeamListModel,
    AddMembersInput,
    UpdateMemberRoleInput,
    AddMembersResultModel,
    ActivityItemModel,
    ActivityFeedModel,
    ActivityQuery,
    UserSearchQuery,
    UserSearchModel,
    UserSummaryModel,
    GitHubIntegrationModel,
    GitHubIntegrationListModel,
    AzureDevOpsIntegrationModel,
    AzureDevOpsIntegrationListModel,
    IntegrationTeamsModel,
    SubscriptionUsageModel,
    ProjectEstimateModel, Subscription, SubscriptionType, SubscriptionStatus
)
from src.error.errors import OperationFailedError


def mock_subscription_usage(user_id: str) -> SubscriptionUsageModel:
    """Mock subscription usage data for a user."""
    logger.info(f"Generating mock subscription usage for user: {user_id}")
    multipliers = {
        "USER": 1,
        "TEAM": 5,
        "COMPANY": 20,
        "PROJECT": 3
    }

    multiplier = multipliers.get(EntityType.USER.value, 1)

    # Generate mock values based on entity type
    files_onboarded = int(50 * multiplier)
    lines_onboarded = int(10000 * multiplier)
    files_touched = int(15 * multiplier)
    lines_added = int(500 * multiplier)
    lines_edited = int(300 * multiplier)
    lines_removed = int(100 * multiplier)
    lines_generated = lines_added + lines_edited + lines_removed

    # Calculate hours saved (roughly 1 hour per 100 lines generated)
    hours_saved = round(lines_generated / 100, 1)

    return SubscriptionUsageModel(
        entity_id=user_id,
        entity_type=EntityType.USER,
        subscription_id=f"sub_{user_id[:8]}",  # Mock subscription ID
        files_onboarded=files_onboarded,
        lines_onboarded=lines_onboarded,
        files_touched=files_touched,
        lines_added=lines_added,
        lines_edited=lines_edited,
        lines_removed=lines_removed,
        lines_generated=lines_generated,
        hours_saved=hours_saved
    )


def mock_user_search(query: UserSearchQuery) -> UserSearchModel:
    """Mock user search results."""
    logger.info(f"Generating mock user search results for query: {query}")

    # Generate mock users based on search criteria
    users = []
    base_count = random.randint(5, 20)

    for i in range(base_count):
        user_id = f"user_{random.randint(1000, 9999)}"
        first_name = random.choice(["John", "Jane", "Bob", "Alice", "Charlie", "Diana"])
        last_name = random.choice(["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia"])

        # Create teams for user
        teams = []
        for j in range(random.randint(0, 3)):
            teams.append({
                "team_id": f"team_{random.randint(100, 999)}",
                "team_name": random.choice(["Frontend", "Backend", "DevOps", "QA"]) + " Team",
                "role": random.choice(["SUPER_ADMIN", "ADMIN", "MEMBER"]),  # TeamRole enum values as strings
                "joined_at": (datetime.now(timezone.utc) - timedelta(days=random.randint(1, 365))).isoformat(),
                "member_count": random.randint(3, 25)
            })

        email_str: EmailStr = f"{first_name.lower()}.{last_name.lower()}@company.com"
        users.append(UserSummaryModel(
            id=user_id,
            email=email_str,
            first_name=first_name,
            last_name=last_name,
            avatar_url="https://avatars.example.com/example.jpg",
            teams=teams
        ))

    # Apply search filter if provided
    if query.search:
        search_term = query.search.lower()
        users = [u for u in users if search_term in str(u.email).lower() or
                 search_term in u.first_name.lower() or
                 search_term in u.last_name.lower()]

    # Apply pagination
    offset = int(query.offset) if query.offset else 0
    limit = int(query.limit) if query.limit else 20
    paginated_users = users[offset:offset + limit]

    return UserSearchModel(
        users=paginated_users,
        total_count=len(users),
    )


def mock_team_list() -> TeamListModel:
    """Mock list of teams for authenticated user."""
    logger.info("Generating mock team list")

    teams = []
    team_count = random.randint(1, 5)

    for i in range(team_count):
        teams.append(mock_team(f"team_{random.randint(1000, 9999)}"))

    return TeamListModel(
        teams=teams,
        total_count=len(teams),
    )


def mock_team(team_id: str, name: Optional[str] = None) -> TeamModel:
    """Mock a single team with members."""
    logger.info(f"Generating mock team data for team_id: {team_id}")

    # Generate team members
    members = []
    member_count = random.randint(2, 10)

    email_str: EmailStr = "<EMAIL>"

    for i in range(member_count):
        members.append(TeamMemberModel(
            user_id=f"user_{random.randint(1000, 9999)}",
            email=email_str,
            first_name=random.choice(["John", "Jane", "Bob", "Alice"]),
            last_name=random.choice(["Smith", "Johnson", "Williams", "Brown"]),
            role=TeamRole.SUPER_ADMIN if i == 0 else random.choice([TeamRole.ADMIN, TeamRole.MEMBER]),
            joined_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 365))
        ))

    return TeamModel(
        id=team_id,
        name=name or f"{random.choice(['Backend', 'Frontend', 'DevOps', 'QA'])} Development Team",
        member_count=len(members),
        created_at=datetime.now(timezone.utc) - timedelta(days=random.randint(30, 365)),
        updated_at=datetime.now(timezone.utc) - timedelta(days=random.randint(0, 30)),
        owner_id=members[0].user_id,
        members=members
    )


def mock_create_team(input_data: CreateTeamInput) -> TeamModel:
    """Mock team creation."""
    logger.info(f"Creating mock team with name: {input_data.name}")

    team_id = f"team_{random.randint(10000, 99999)}"
    team = mock_team(team_id, input_data.name)

    # Add initial members if provided
    if input_data.initial_members:
        for member_input in input_data.initial_members:
            team.members.append(TeamMemberModel(
                user_id=f"user_{random.randint(1000, 9999)}",
                email=member_input.email,
                first_name="New",
                last_name="Member",
                role=member_input.role or input_data.default_role or TeamRole.MEMBER,
                joined_at=datetime.now(timezone.utc)
            ))
        team.member_count = len(team.members)

    return team


def mock_update_team(team_id: str, input_data: UpdateTeamInput) -> TeamModel:
    """Mock team update."""
    logger.info(f"Updating mock team {team_id} with data: {input_data}")

    team = mock_team(team_id)
    if input_data.name:
        team.name = input_data.name
    team.updated_at = datetime.now(timezone.utc)

    return team


def mock_add_members(team_id: str, input_data: AddMembersInput) -> AddMembersResultModel:
    """Mock adding members to a team."""
    logger.info(f"Adding mock members to team {team_id}: {input_data.emails}")

    added_members = []
    pending_emails = []

    for email in input_data.emails:
        # Randomly decide if user exists or needs to register
        if random.random() > 0.3:  # 70% chance user exists
            # Handle EmailStr properly by converting to string for parsing
            email_str = str(email)
            name_parts = email_str.split('@')[0].split('.')
            first_name = name_parts[0].capitalize() if name_parts else "User"
            last_name = name_parts[1].capitalize() if len(name_parts) > 1 else "User"

            added_members.append(TeamMemberModel(
                user_id=f"user_{random.randint(1000, 9999)}",
                email=email,
                first_name=first_name,
                last_name=last_name,
                role=input_data.default_role or TeamRole.MEMBER,
                joined_at=datetime.now(timezone.utc)
            ))
        else:
            pending_emails.append(email)

    return AddMembersResultModel(
        added_members=added_members,
        pending_emails=pending_emails,
        sent_on=datetime.now(timezone.utc)
    )


def mock_update_member_role(team_id: str, user_id: str, input_data: UpdateMemberRoleInput) -> TeamMemberModel:
    """Mock updating a team member's role."""
    logger.info(f"Updating role for user {user_id} in team {team_id} to {input_data.role}")

    email_str : EmailStr = f"user_{user_id}@company.com"

    return TeamMemberModel(
        user_id=user_id,
        email=email_str,
        first_name="Updated",
        last_name="Member",
        role=input_data.role,
        joined_at=datetime.now(timezone.utc) - timedelta(days=random.randint(30, 365))
    )


def mock_activity_feed(entity_id: str, query: Optional[ActivityQuery] = None) -> ActivityFeedModel:
    """Mock activity feed for team or project."""
    logger.info(f"Generating mock activity feed for entity: {entity_id}")

    activities = []
    activity_count = random.randint(5, 20)

    for i in range(activity_count):
        activity_type = random.choice(list(ActivityType))
        timestamp = datetime.now(timezone.utc) - timedelta(hours=random.randint(1, 720))

        # Filter by since date if provided
        if query and query.since:
            since_date = query.since
            if timestamp < since_date:
                continue

        metadata = {}
        if activity_type in [ActivityType.PROJECT_CREATED, ActivityType.PROJECT_UPDATED]:
            metadata["project_name"] = f"Project {random.randint(1, 100)}"
        elif activity_type in [ActivityType.MEMBER_ADDED, ActivityType.MEMBER_REMOVED]:
            metadata["member_email"] = f"user{random.randint(1, 100)}@company.com"

        activities.append(ActivityItemModel(
            id=f"activity_{random.randint(10000, 99999)}",
            type=activity_type,
            user_id=f"user_{random.randint(1000, 9999)}",
            project_id=f"project_{random.randint(100, 999)}",
            team_id=entity_id if "team" in entity_id else f"team_{random.randint(100, 999)}",
            timestamp=timestamp,
            metadata=metadata
        ))

    # Sort by timestamp descending
    activities.sort(key=lambda x: x.timestamp, reverse=True)

    # Apply limit
    limit = 20
    if query and query.limit:
        limit = int(query.limit)

    return ActivityFeedModel(
        activities=activities[:limit],
        total_count=len(activities),
    )


def mock_project_list(team_id: Optional[str] = None):
    """Mock project list with optional team filter."""
    logger.info(f"Generating mock project list for team: {team_id}")

    projects = []
    project_count = random.randint(3, 10)

    for i in range(project_count):
        project = {
            "id": f"project_{random.randint(1000, 9999)}",
            "name": f"{random.choice(['Mobile', 'Web', 'API', 'Data'])} {random.choice(['App', 'Service', 'Platform', 'Tool'])}",
            "description": "A mock project for demonstration purposes",
            "created_at": (datetime.now(timezone.utc) - timedelta(days=random.randint(30, 365))).isoformat() + "Z",
            "updated_at": (datetime.now(timezone.utc) - timedelta(days=random.randint(0, 30))).isoformat() + "Z",
            "team_id": team_id or f"team_{random.randint(100, 999)}",
            "owner_id": f"user_{random.randint(1000, 9999)}",
            "status": random.choice(["active", "completed", "archived"])
        }

        # Only include if matches team filter
        if not team_id or project["team_id"] == team_id:
            projects.append(project)

    return {
        "projects": projects,
        "total_count": len(projects),
    }


def mock_project_estimate(project_id: str, techspec_id: str) -> ProjectEstimateModel:
    """Mock project tech spec estimate."""
    logger.info(f"Generating mock estimate for project {project_id}, techspec {techspec_id}")

    completion = random.uniform(10.0, 90.0)

    return ProjectEstimateModel(
        timestamp=datetime.now(timezone.utc),
        completion_percentage=round(completion, 1),
        estimated_time_remaining=random.randint(10, 200),
        estimated_additional_lines=random.randint(500, 5000),
        engineering_hours_remaining=round(random.uniform(10.0, 100.0), 1),
        engineering_hours_saved=round(random.uniform(5.0, 50.0), 1)
    )


def mock_integration_teams(integration_type: IntegrationType) -> IntegrationTeamsModel:
    """Mock teams with integration access."""
    logger.info(f"Generating mock integration teams for type: {integration_type}")

    teams = []
    team_count = random.randint(3, 8)

    for i in range(team_count):
        teams.append(TeamModel(
            id=f"team_{random.randint(1000, 9999)}",
            name=f"Team {random.randint(100, 999)}",
            company_id=f"company_{random.randint(1000, 9999)}",
            created_at=datetime.now(timezone.utc),
            member_count=random.randint(1, 10),
            members=[],
            owner_id=f"user_{random.randint(1000, 9999)}",
            updated_at=datetime.now(timezone.utc),
            is_default=False
        ))

    return IntegrationTeamsModel(
        teams=teams,
    )


def mock_user_integrations(integration_type: IntegrationType):
    """Mock user's available integrations."""
    if integration_type == IntegrationType.GITHUB:
        return _mock_user_github_integrations()
    elif integration_type == IntegrationType.AZURE_DEVOPS:
        return _mock_user_azure_devops_integrations()
    else:
        logger.error(f"Unsupported integration type: {integration_type}")
        raise OperationFailedError(f"Unsupported integration type: {integration_type}") # mock so won't be reached

def _mock_user_github_integrations():
    logger.info(f"Generating mock user integrations for azure devops")

    integrations = []
    integration_count = random.randint(1, 5)

    for i in range(integration_count):
        shared_teams = []
        for j in range(random.randint(0, 3)):
            shared_teams.append({
                "team_id": f"team_{random.randint(100, 999)}",
                "team_name": f"{random.choice(['Frontend', 'Backend', 'DevOps'])} Team",
                "permission_level": random.choice(["read", "write", "admin"]),
                "shared_on": datetime.now(timezone.utc) - timedelta(days=random.randint(1, 90))
            })

        integrations.append(GitHubIntegrationModel(
            id=f"integration_{random.randint(1000, 9999)}",
            target_name=f"GitHub Integration Name",
            owner_id=f"user_{random.randint(1000, 9999)}",
        ))

    return GitHubIntegrationListModel(
        integrations=integrations,
        total_count=len(integrations)
    )

def _mock_user_azure_devops_integrations():
    logger.info(f"Generating mock user integrations for azure devops")

    integrations = []
    integration_count = random.randint(1, 5)

    for i in range(integration_count):

        integrations.append(AzureDevOpsIntegrationModel(
            id=f"integration_{random.randint(1000, 9999)}",
            target_name=f"Azure DevOps Integration Name",
            owner_id=f"user_{random.randint(1000, 9999)}",
        ))

    return AzureDevOpsIntegrationListModel(
        integrations=integrations,
        total_count=len(integrations)
    )


# Success response helpers
def mock_delete_success(entity_type: str, entity_id: str) -> dict:
    """Mock successful deletion response."""
    logger.info(f"Mock deletion successful for {entity_type}: {entity_id}")
    return {
        "message": f"{entity_type.capitalize()} {entity_id} successfully deleted",
        "status": "success"
    }


def mock_share_success(entity_type: str, entity_id: str, target_type: str, target_id: str) -> dict:
    """Mock successful sharing response."""
    logger.info(f"Mock sharing successful: {entity_type} {entity_id} with {target_type} {target_id}")
    return {
        "message": f"{entity_type.capitalize()} {entity_id} successfully shared with {target_type} {target_id}",
        "status": "success"
    }


def mock_unshare_success(entity_type: str, entity_id: str, target_type: str, target_id: str) -> dict:
    """Mock successful unsharing response."""
    logger.info(f"Mock unsharing successful: {entity_type} {entity_id} from {target_type} {target_id}")
    return {
        "message": f"{entity_type.capitalize()} {entity_id} successfully unshared from {target_type} {target_id}",
        "status": "success"
    }

def generate_mock_subscription_status() -> Subscription:
    """Generate mock subscription status"""

    current_time = datetime.now(timezone.utc)

    return Subscription(
        stripe_customer_id=f"cus_{uuid.uuid4().hex[:14]}",
        stripe_subscription_id=f"sub_{uuid.uuid4().hex[:14]}",
        plan_type=SubscriptionType.PRO,  # Using the enum
        start_date=current_time - timedelta(days=30),
        end_date=current_time + timedelta(days=335),
        trial_start_date=current_time - timedelta(days=37),
        trial_end_date=current_time - timedelta(days=30),
        current_period_start_date=current_time - timedelta(days=5),
        current_period_end_date=current_time + timedelta(days=25),
        status=SubscriptionStatus.ACTIVE,  # Using the enum
        remaining_runs=random.randint(25, 100),
        is_trialing=False,
        has_trial_received=True
    )