"""
Shared utility functions for API endpoints.

This module contains common helper functions used across all API blueprints.
"""

from flask import jsonify, request
from werkzeug.exceptions import BadRequest, UnsupportedMediaType
from blitzy_utils.logger import logger


def handle_not_implemented(endpoint_name: str, method: str) -> tuple:
    """
    Helper function to handle 501 Not Implemented responses consistently.

    Args:
        endpoint_name (str): Name of the endpoint being called
        method (str): HTTP method being used

    Returns:
        tuple: JSON response and status code
    """
    logger.info(f"{method} {endpoint_name} endpoint called - returning 501 Not Implemented")
    return jsonify({
        'error': 'Not Implemented',
        'message': f'{endpoint_name} endpoint is not yet implemented',
        'status_code': 501
    }), 501


def safe_log_request_data():
    """Safely attempt to log request data for debugging."""
    try:
        request_data = request.get_json(silent=True)
        logger.debug(f"Request data: {request_data}")
    except (BadRequest, UnsupportedMediaType) as e:
        logger.debug(f"Could not parse JSON request: {e}")
    except AttributeError:
        logger.debug("Request data: No JSON data available")


def build_url_with_params(base_url: str, request_args) -> str:
    """Build URL with preserved query parameters."""
    if request_args:
        query_params = "&".join([f"{k}={v}" for k, v in request_args.items()])
        return f"{base_url}?{query_params}"
    return base_url