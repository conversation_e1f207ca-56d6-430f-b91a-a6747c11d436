import os
from flask import Blueprint, redirect, request
from blitzy_utils.logger import logger

from src.api.utils.shared_utils import build_url_with_params
from src.middleware.decorators import get_user_info

# Get admin service URL from environment
ADMIN_SERVICE_URL = os.environ.get('SERVICE_URL_ADMIN')

subscriptions_bp = Blueprint('subscriptions', __name__, url_prefix='/v1/subscription')


# Subscription and Usage Endpoints

@subscriptions_bp.route('<string:entity_type>/<string:entity_id>/usage', methods=['GET'])
@get_user_info
def get_subscription_usage(user_info, entity_type: str, entity_id: str):
    """Mock endpoint for user subscription usage"""
    logger.info(f"Redirecting to admin service for user {user_info['id']} {entity_type} {entity_id} subscription usage")

    admin_url = f"{ADMIN_SERVICE_URL}/v1/admin/subscription/{entity_type}/{entity_id}/usage"
    admin_url = build_url_with_params(admin_url, request.args)
    return redirect(admin_url, code=307)
