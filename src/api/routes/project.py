from datetime import datetime
from http import HTTPStatus
from typing import Any, Dict

from blitzy_utils.enums import ProjectPhase
from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from common_models.db_client import get_db_session
from common_models.models import (Job, Project, PromptStatus, Status,
                                  TechnicalSpec, User)
from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import (datetime_to_epoch,
                                                   map_to_model)
from sqlalchemy import Row, text
from sqlalchemy.exc import OperationalError
from tenacity import (retry, retry_if_exception_type, stop_after_attempt,
                      wait_exponential)

from src.api.models import (ActivityFeedModel, ActivityQuery,
                            CreateProjectInput, DocumentPromptInput,
                            GetAllProject, GetGithubProjectRepositoryOutput,
                            GetProjectDetailOutput, GetProjectJobStatusOutput,
                            GetProjectListOutput, ProjectEstimateModel,
                            Status200, Status201, Status400, Status404,
                            UpdateProjectInput)
from src.api.routes.attachments import attachments_bp
from src.api.routes.code_gen import code_gen_bp
from src.api.routes.job import get_repo_name
from src.api.routes.project_github_repos import project_github_bp
from src.api.routes.tech_spec import tech_spec_bp
from src.api.utils.mocks import (mock_activity_feed, mock_project_estimate,
                                 mock_share_success, mock_unshare_success)
from src.api.utils.project_utils import (PRODUCT_INITIAL_TYPE_MAPPER,
                                         trigger_document_generation,
                                         upload_document_prompt_to_gcs,
                                         upload_project_prompt_to_gcs,
                                         validate_document_submission)
from src.error.base_error import BlitzyError
from src.error.errors import JobLimitExhaustedError
from src.middleware.decorators import get_user_info
from src.service.job_service import save_job
from src.service.project_service import (
    get_all_projects_by_user_id, get_project_by_id_user_id,
    get_project_by_user_id, get_project_by_user_id_with_relations,
    get_project_with_latest_spec, save_project,
    update_project_filter_by_user_id_project_id)
from src.service.tech_spec_service import update_tech_spec_by_project_id
from src.service.user_service import get_user_by_id

project_bp = Blueprint("project", __name__, url_prefix="/v1/project")
project_bp.register_blueprint(attachments_bp)
project_bp.register_blueprint(tech_spec_bp)
project_bp.register_blueprint(code_gen_bp)
project_bp.register_blueprint(project_github_bp)

transformer = {
    "createdAt": datetime_to_epoch,
    "updatedAt": datetime_to_epoch,
    "promptUpdatedAt": datetime_to_epoch,
}

field_mapping = {
    "owner": "user.email",
}


@project_bp.route("", methods=["POST"])
@validate_request(CreateProjectInput)
@get_user_info
@flask_pydantic_response
def create_project(user_info, payload: CreateProjectInput):
    with get_db_session() as session:
        repo_prefix = payload.name.replace(" ", "-").lower()
        repo_url = get_repo_name(repo_prefix)
        project = Project(
            user_id=user_info["id"],
            name=payload.name,
            status=Status.GITHUB_PENDING,
            initial_type=PRODUCT_INITIAL_TYPE_MAPPER[payload.type],
            repo_prefix=repo_prefix,
            repo_url=repo_url,
        )
        project = save_project(project, session)

        session.commit()
        return Status201(message="Success", id=project.id), 201


# TODO: Implement pagination.
@project_bp.route("", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_project(user_info):
    team_id = request.args.get('team_id')  # TODO: add team level filtering
    if team_id:
        logger.info(f"Getting user projects for team: {team_id}")
    project_list = get_projects_by_user_id(user_info["id"])
    return project_list, 200


@project_bp.route("/<project_id>", methods=["PUT"])
@validate_request(UpdateProjectInput)
@get_user_info
@flask_pydantic_response
def update_project(user_info, project_id, payload: UpdateProjectInput):
    with get_db_session() as session:
        session.execute(text("SET TRANSACTION ISOLATION LEVEL SERIALIZABLE"))
        project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
        if project_info is None:
            logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
            return Status404(message="Project not found"), 400

        if project_info.prompt_status == PromptStatus.SUBMITTED:
            logger.warning(f"Project with ID {project_id} already submitted.")
            return Status400(message=f"Project with ID {project_id} already submitted"), 400

        handle_update_project(user_info, project_info, payload)

        return Status200(message="Success"), 200


@project_bp.route("/<project_id>", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_project_by_id(user_info, project_id):
    with get_db_session() as session:
        project = get_project_with_latest_spec(user_info["id"], project_id, session)
        if not project:
            logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
            return Status404(message=f"Project with ID {project_id} does not exist"), 404

        project_pydantic = map_to_project_pydantic(project)
        return project_pydantic, 200


@project_bp.route("/<project_id>", methods=["DELETE"])
@get_user_info
@flask_pydantic_response
def delete_project(user_info: Dict[str, Any], project_id: str):
    with get_db_session() as session:
        project = get_project_by_id_user_id(user_info["id"], project_id, session)
        if not project:
            logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
            return Status404(message=f"Project with ID {project_id} does not exist"), 404
        project.soft_delete()
        session.commit()
        return Status200(message="Success"), 200


@project_bp.route("/<project_id>/job/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
@retry(
    retry=retry_if_exception_type(OperationalError),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def get_project_job_status(user_info, project_id):
    with get_db_session() as session:
        project = get_project_by_user_id_with_relations(user_info["id"], project_id, session)
        if not project:
            logger.warning(f"Project with ID {project_id} not found for user {user_info['id']}.")
            return Status404(message=f"Project with ID {project_id} does not exist"), 404
        project_status = get_project_status(project)
        return project_status, 200


def get_projects_by_user_id(user_id: str) -> GetProjectListOutput:
    """
    Get list of projects by user id.
    :param user_id: User ID.
    :return: List of projects if any.
    """
    try:
        with get_db_session() as session:
            projects = get_all_projects_by_user_id(user_id, session)
            user = get_user_by_id(user_id, session)
            project_models = [map_project_db_to_pydantic(project, user) for project in projects]
            project_list_output = GetProjectListOutput()
            project_list_output.projects = project_models
            return project_list_output
    finally:
        session.close()


@project_bp.route("/<project_id>/github/repo", methods=["GET"])
@flask_pydantic_response
def get_github_repo_by_project_id(project_id: str):
    """Get the github repository by project id."""
    github_repo = get_github_repo_from_github_service(project_id)
    return github_repo, 200


@project_bp.route("/<project_id>/document/prompt", methods=["POST"])
@validate_request(DocumentPromptInput)
@get_user_info
@flask_pydantic_response
def handle_document_prompt_submission(user_info: Dict[str, Any], project_id: str, payload: DocumentPromptInput):
    """Upload the document prompt to gcs"""
    project_info = get_project_by_user_id(user_info["id"], project_id)
    upload_document_prompt_to_gcs(project_info, payload, project_info.repo_url, user_info)
    return Status200(message="Success"), 200


def get_project_status(project: Project) -> GetProjectJobStatusOutput:
    """
    Generate a project job status object from Project object.
    :param project: Project object.
    :return: Project job status object.
    """
    project_status = GetProjectJobStatusOutput(status=project.status)
    return project_status


def handle_update_project(user_info: Dict[str, Any], project: Project, payload: UpdateProjectInput):
    """
    Handle update project call.
    :param user_info: User info.
    :param project: Project object.
    :param payload: Payload sent by the user.
    """
    update_payload = {
        Project.name: payload.name,
    }

    submit_project(user_info, project, update_payload, payload)


def save_project_draft_mode(user_info: Dict[str, Any], project: Project, update_payload):
    """
    Saves project in draft mode.

    :param user_info: User info.
    :param project: Project info object.
    :param update_payload: Update database object.
    """
    with get_db_session() as session:
        update_payload[Project.prompt_status] = PromptStatus.DRAFT
        update_project_filter_by_user_id_project_id(user_info["id"], project.id, update_payload, session)
        session.commit()


def submit_project(user_info: Dict[str, Any], project: Project, update_payload: Dict[Any, Any],
                   payload: UpdateProjectInput):
    """
    Submit project for software req generation.

    :param user_info: User info.
    :param project: Project info object.
    :param update_payload: Update database object.
    :param payload: Payload send by the user.
    """
    with get_db_session() as session:
        # Update project table and software requirement table.
        # Set project status to IN_PROGRESS.
        logger.info(f"Initiating project submission for {project.id}")
        allowed = validate_document_submission(user_info["id"])

        if not allowed:
            raise JobLimitExhaustedError(
                "You have exceeded the maximum number of submissions per day. Please try again tomorrow.")

        update_payload.update({
            Project.prompt_status: PromptStatus.SUBMITTED,
            Project.prompt_updated_at: datetime.utcnow(),
            Project.status: Status.IN_PROGRESS,
        })
        update_project_filter_by_user_id_project_id(user_info["id"], project.id, update_payload, session)

        job = Job(
            status=Status.TODO,
            project_id=project.id,
            stage_type=ProjectPhase.TECHNICAL_SPECIFICATION.value
        )
        job = save_job(job, session)

        # Update technical specification table with job id.
        technical_specification_payload = {
            TechnicalSpec.job_id: job.id,
            TechnicalSpec.status: Status.QUEUED,
        }
        update_tech_spec_by_project_id(project.id, technical_specification_payload, session)

        logger.info(f"Attempting to upload prompt to GCS for project {project.id}")
        upload_project_prompt_to_gcs(project, payload, project.repo_url, user_info)
        logger.info(f"Prompt uploaded to GCS for project {project.id}")

        logger.info(f"Attempting to trigger document generation for project {project.id}")
        trigger_document_generation(user_info["id"], payload.prompt, project.repo_url, 0, project.id, job.id, "", "")
        logger.info(f"Document generation triggered for project {project.id}")

        session.commit()
        logger.info(f"Project {project.id} submitted successfully")


def map_project_db_to_pydantic(project: Row, user: User) -> GetAllProject:
    project_model = GetAllProject()
    project_model.id = project[0]
    project_model.name = project[1]
    project_model.status = project[2]
    project_model.promptStatus = project[3]
    if project[4]:
        project_model.promptUpdatedAt = project[4].timestamp()
    project_model.repoPrefix = project[5]
    project_model.repoUrl = project[6]
    project_model.createdAt = project[7].timestamp()
    project_model.updatedAt = project[8].timestamp()
    project_model.isDisabled = project[9]
    project_model.disableReason = project[10]
    project_model.owner = user.email
    return project_model


def get_github_repo_from_github_service(project_id: str) -> GetGithubProjectRepositoryOutput:
    with ServiceClient() as client:
        response = client.get("github_handler", f"/v1/project/{project_id}/github/repo")

        if response.status_code == 200:
            model = generate_github_repo_pydantic_model(response.json())
            return model

        if response.status_code == 404:
            raise BlitzyError(message=f"No github repository found for {project_id}", status_code=404)

        raise Exception(f"Something went wrong. {response.status_code} - {response.text}")


def generate_github_repo_pydantic_model(github_repo: Dict[str, Any]) -> GetGithubProjectRepositoryOutput:
    github_repo_model = GetGithubProjectRepositoryOutput()
    github_repo_model.repo_full_name = github_repo["repoFullName"]
    github_repo_model.repo_id = github_repo["repoId"]
    github_repo_model.repo_name = github_repo["repoName"]
    github_repo_model.status = github_repo["status"]
    return github_repo_model


def map_to_project_pydantic(result: Dict[str, Any]) -> GetProjectDetailOutput:
    project_model = map_to_model(result["project"], GetProjectDetailOutput)
    if result["latest_technical_spec"]:
        project_model.technicalSpecAvailable = True
    if result["latest_code_generation"]:
        project_model.codeGenerationAvailable = True
    return project_model


@project_bp.route('/<string:project_id>/share/<string:team_id>', methods=['POST'])
@get_user_info
def share_project(user_info, project_id: str, team_id: str):
    """Share a project with a team."""
    logger.info(f"Sharing project {project_id} with team {team_id} by user: {user_info['id']}")

    # Return mock success response
    return mock_share_success("project", project_id, "team", team_id), HTTPStatus.OK


@project_bp.route('/<string:project_id>/share/<string:team_id>', methods=['DELETE'])
@get_user_info
def unshare_project(user_info, project_id: str, team_id: str):
    """Unshare project from a team."""
    logger.info(f"Unsharing project {project_id} from team {team_id} by user: {user_info['id']}")

    # Return mock success response
    return mock_unshare_success("project", project_id, "team", team_id), HTTPStatus.OK


@project_bp.route('/<string:project_id>/activity', methods=['GET'])
@get_user_info
@validate_request(ActivityQuery)
@flask_pydantic_response
def get_project_activity(user_info, project_id: str, payload: ActivityQuery) -> ActivityFeedModel:
    """Get project activity (with team filter)."""
    logger.info(f"Getting activity for project {project_id}, requested by user: {user_info['id']}")

    # Return mock activity feed
    return mock_activity_feed(f"project_{project_id}", payload)


@project_bp.route('/<string:project_id>/tech-spec/<string:tech_spec_id>/estimate', methods=['GET'])
@get_user_info
@flask_pydantic_response
def get_project_tech_spec_estimate(user_info, project_id: str, tech_spec_id: str) -> ProjectEstimateModel:
    """Get predictive completion data from tech spec."""
    logger.info(
        f"Getting estimate for project {project_id}, tech-spec {tech_spec_id}, requested by user: {user_info['id']}")

    # Return mock estimate
    return mock_project_estimate(project_id, tech_spec_id)
