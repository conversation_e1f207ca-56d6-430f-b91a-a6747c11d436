import dataclasses
import threading
from datetime import datetime
from typing import Any, Dict, Optional

from blitzy_utils.common import generate_technical_spec_document_path
from blitzy_utils.consts import (DEFAULT_NAME, DOWNLOAD_CODE_TOPIC,
                                 GENERATE_REVERSE_DOCUMENT_TOPIC)
from blitzy_utils.enums import (BackpropChangeMode, BackpropCommand,
                                ProjectPhase)
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (BlitzyCommitStatus, BranchLockReason,
                                  GitHubProjectRepo, Project, ProjectRun,
                                  ProjectRunStage, ProjectRunStatus,
                                  ProjectRunType, ReportStatus, Status,
                                  TechnicalSpec, TechSpecJobType, UsageType, User)
from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model
from sqlalchemy.orm import Session

from src.api.models import CodeGeneration as CodeGenerationModel
from src.api.models import (GetTechnicalSpecificationDocumentOutput,
                            GetTechnicalSpecJobStatusOutput,
                            PDFInProgressResponse, PDFReadyResponse, Status200,
                            Status201, Status404, Status503,
                            ParseMarkdownRequest, ParseMarkdownResponse,
                            ParseJsonRequest, ParseJsonResponse)
from src.api.models import TechnicalSpec as TechnicalSpecModel
from src.api.models import TechSpecJobType as TechSpecJobTypeModel
from src.api.models import TechSpecPromptInput, TechSpecSyncStatusOutput
from src.api.utils.code_gen_utils import validate_code_gen_submit
from src.api.utils.gcs_utils import check_file_size, upload_to_gcs
from src.api.utils.github_installation_utils import \
    get_github_repo_from_github_handler_with_scv_inference
from src.api.utils.github_utils import \
    get_branch_head_commit_from_github_handler
from src.api.utils.project_utils import (IN_PROGRESS_JOB_STATUS,
                                         trigger_document_generation,
                                         upload_project_prompt_to_gcs,
                                         validate_document_submission)
from src.api.utils.tech_spec_utils import (
    create_branch_lock, generate_tech_spec_pdf_presigned_url,
    generate_technical_spec_document_url,
    get_back_prop_tech_spec_presigned_url, get_tech_spec_presigned_url,
    handle_common_tech_spec_submit, handle_new_product_tech_spec_submit,
    insert_tech_spec_document_context_record, revert_tech_spec_by_project_id,
    trigger_tech_spec_pdf_generation, upload_tech_spec_prompt_to_gcs,
    validate_subscription, validate_tech_spec_submit)
from src.api.utils.tech_spec_parser import (
    TechSpecParser, validate_markdown_content, validate_json_structure)
from src.blitzy.utils import publish_notification
from src.consts import (DOCUMENT_EXPIRY_TIME, GCS_BUCKET_NAME,
                        IN_PROGRESS_REPORT_STATUS, LATEST, PROJECT_ID,
                        TECHNICAL_SPECIFICATION_FILE_SIZE, publisher)
from src.error.errors import (BranchLockedError,
                              CodeGenInformationNotFoundError,
                              FailedToUploadFileError, FileTooLargeError,
                              FlaskFileNotFound, GitHubIntegrationError,
                              GithubRepoAlreadyInSync,
                              InvalidGithubRepoOnboardError,
                              JobAlreadyRunningError, JobNotCompleteError,
                              JobTypeNotSupportedError,
                              LatestTechSpecNotFoundError,
                              OperationNotSupportedError, ProjectNotFoundError,
                              TechnicalSpecificationNotFoundError,
                              ResourceNotFound, UnauthorizedError, InvalidInput,
                              OperationFailedError)
from src.middleware.decorators import get_user_info
from src.service.blitzy_commit_service import get_blitzy_commit_by_code_gen_id
from src.service.branch_lock_service import has_active_lock
from src.service.code_gen_service import (
    get_code_gen_by_project_id_and_tech_spec_id,
    get_latest_code_gen_by_project_id)
from src.service.github_branch_pattern_project import \
    get_branch_pattern_by_project_id
from src.service.github_project_repo_service import (
    get_source_github_project_repo_by_id, update_github_project_by_project_id)
from src.service.job_service import get_job_by_project_stage_type
from src.service.project_run_service import (
    get_project_run_by_project_and_tech_spec_id, save_project_run,
    update_project_run_with_tech_spec_id)
from src.service.project_service import (get_project_by_user_id,
                                         get_project_by_user_id_with_relations,
                                         update_project_timeline)
from src.service.team_member_service import get_team_member_by_user_id
from src.service.tech_spec_service import (
    get_all_tech_specs_by_project_id_order_by_created_at,
    get_in_progress_tech_spec_by_project_id_and_job_type,
    get_in_progress_tech_spec_by_project_id_and_status,
    get_latest_tech_spec_by_project_id, get_latest_tech_spec_with_new_version,
    get_tech_spec_by_id_and_project_id, get_tech_spec_by_project_id)

tech_spec_bp = Blueprint("tech_spec", __name__)
sync_tech_spec_lock = threading.Lock()
# Project-specific locks for different job types
project_locks = {
    "add_feature": {},
    "refactor_code": {},
    "sync_tech_spec": {},
    "fix_bugs": {},
    "fix_cves": {},
    "add_testing": {},
    "document_code": {},
    "custom": {}
}
locks_mutex = threading.Lock()


def get_project_lock(project_id: str, job_type: str) -> threading.Lock:
    """
    Get or create a project-specific lock for the given job type.
    This ensures that locks are scoped to individual projects rather than being global.

    :param project_id: The project ID to get the lock for.
    :param job_type: The job type (add_feature, refactor_code, sync_tech_spec).
    :return: A threading.Lock object specific to the project and job type.
    """
    with locks_mutex:
        if project_id not in project_locks[job_type]:
            logger.debug(f"Creating a new lock for the project {project_id} and job type {job_type}.")
            project_locks[job_type][project_id] = threading.Lock()
        return project_locks[job_type][project_id]


@dataclasses.dataclass
class TechSpecNotificationPayload:
    tech_spec_id: str
    repo_name: str
    repo_id: str
    branch_id: str
    branch_name: str
    company_id: str
    user_id: str
    team_id: str
    job_id: str
    project_id: str
    head_commit_hash: str
    prev_head_commit_hash: str
    propagate: bool
    git_project_repo_id: str
    document_mode: Optional[str] = None
    previous_tech_spec_id: Optional[str] = None
    job_type: Optional[str] = None


@tech_spec_bp.route("/<project_id>/tech-spec", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    with get_db_session() as session:
        technical_spec = (session.query(TechnicalSpec)
                          .filter(TechnicalSpec.project_id == project_id).first())
        technical_spec_model = map_to_model(technical_spec, TechnicalSpecModel)
        return technical_spec_model, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_by_id(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    tech_spec = fetch_tech_spec_by_id_and_project_id(project_id, tech_spec_id)
    if not tech_spec:
        raise TechnicalSpecificationNotFoundError(f"Failed to find tech spec with project id {project_id}")

    return tech_spec, 200


@tech_spec_bp.route("/<project_id>/tech-spec/job/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_job_status(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        logger.warning(f"Failed to find project with ID {project_id} for user {user_info['id']}.")
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    job = get_job_by_project_stage_type(project_id, ProjectPhase.TECHNICAL_SPECIFICATION)
    if not job:
        return Status503(message="Job information is not available yet."), 503
    job_model = map_to_model(job, GetTechnicalSpecJobStatusOutput)
    return job_model, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/document", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_document_by_id(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    tech_spec = get_tech_spec_by_project_id_and_id(project_id, tech_spec_id)
    if not tech_spec:
        raise TechnicalSpecificationNotFoundError(f"Failed to find tech spec with project id {project_id}")

    # Generate Presigned URL.
    if tech_spec.job_type == TechSpecJobType.NEW_PRODUCT:
        if tech_spec.job_metadata:
            project_info.repo_url = tech_spec.job_metadata.get("repo_name")
            url = get_tech_spec_presigned_url(project_info, tech_spec.id, user_info["company_id"])
        else:
            url = get_tech_spec_presigned_url(project_info, tech_spec.id, user_info["company_id"])
    else:
        url = get_back_prop_tech_spec_presigned_url(project_info, tech_spec, user_info["company_id"])
    output = GetTechnicalSpecificationDocumentOutput()
    output.preSignedURL = url
    output.expiryInSeconds = DOCUMENT_EXPIRY_TIME
    return output, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/code-gen", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_code_gen_by_id(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    code_generation = get_code_gen_by_project_id_and_tech_spec_id(project_id, tech_spec_id)
    if not code_generation:
        raise CodeGenInformationNotFoundError(message="Code generation is not available yet.")
    code_generation_model = map_to_model(code_generation, CodeGenerationModel)
    return code_generation_model, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/document", methods=["PUT"])
@get_user_info
@flask_pydantic_response
def update_tech_spec_document(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    if "file" not in request.files:
        logger.warning(f"Files found in request {request.files}")
        raise FlaskFileNotFound("File not found in the API")

    # Validate file size.
    file = request.files["file"]
    size_check = check_file_size(file, TECHNICAL_SPECIFICATION_FILE_SIZE)

    if not size_check["success"]:
        logger.warning(f"File size exceeds for project {project_id}.")
        raise FileTooLargeError(f"File size exceeds {TECHNICAL_SPECIFICATION_FILE_SIZE} MB.")

    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    # Check if technical specification job is in progress. If it's in progress raise exception.
    tech_spec = get_tech_spec_by_project_id_and_id(project_id, tech_spec_id)
    if tech_spec.status in IN_PROGRESS_JOB_STATUS and tech_spec.status != Status.TODO:
        raise JobNotCompleteError(f"Technical specification job for project {project_id}"
                                  f" has not completed yet. Cannot submit file.")

    # TODO (Chaitanya): Fix this messy logic after sometime. Do it when projects are not being used anymore.
    if tech_spec.job_type == TechSpecJobType.NEW_PRODUCT:
        if tech_spec.job_metadata:
            repo_name = tech_spec.job_metadata.get("repo_name")
            file_path = generate_technical_spec_document_url(repo_name, tech_spec.id)
        else:
            file_path = generate_technical_spec_document_url(project_info.repo_url, tech_spec.id)
    else:
        file_path = generate_technical_spec_document_path(tech_spec.job_metadata, GCS_BUCKET_NAME)

    result = upload_to_gcs(file_obj=file, blob_name=file_path)

    if not result["success"]:
        logger.warning(f"Failed to upload file for project {project_id} with error {result['message']}")
        raise FailedToUploadFileError(f"Failed to upload file for project {project_id}")

    logger.info(f"File uploaded successfully for project {project_id} with path {result['file_path']}")
    logger.info(f"Triggering PDF generation for technical spec with project ID {project_id}.")
    trigger_tech_spec_pdf_generation(project_info, tech_spec)
    return Status200(message="Success"), 200


@tech_spec_bp.route("/<project_id>/tech-spec/document/pdf", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_document_pdf(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    technical_spec = get_tech_spec_by_project_id(project_id)
    if not technical_spec:
        raise TechnicalSpecificationNotFoundError(f"Failed to find tech spec with project id {project_id}")

    if technical_spec.pdf_report_status is None:
        logger.warning(f"PDF report status is None for the project {project_id}. Triggering new generation.")
        response = trigger_tech_spec_pdf_generation(project_info, technical_spec)
        logger.info(f"Notification to platform event listener sent. PDF will be generated for"
                    f" tech spec with project ID {project_id}.")
        return response, 202

    if technical_spec.pdf_report_status == ReportStatus.IN_PROGRESS:
        logger.info(f"Technical spec PDF report status is IN_PROGRESS for the project {project_id}.")
        response = PDFInProgressResponse()
        response.status = ReportStatus.IN_PROGRESS.value
        return response, 202

    logger.info("Technical spec PDF report is ready, generating pre signed URL")
    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    url = generate_tech_spec_pdf_presigned_url(project_info, technical_spec, company_id)
    response = PDFReadyResponse()
    response.preSignedURL = url
    response.expiryInSeconds = DOCUMENT_EXPIRY_TIME
    return response, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/document/pdf", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_document_pdf_by_id(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    tech_spec = get_tech_spec_by_project_id_and_id(project_id, tech_spec_id)
    if not tech_spec:
        raise TechnicalSpecificationNotFoundError(f"Failed to find tech spec with project id {project_id}")

    if tech_spec.pdf_report_status in IN_PROGRESS_REPORT_STATUS:
        logger.info(f"Technical spec PDF report status is IN_PROGRESS for the project {project_id}.")
        response = PDFInProgressResponse()
        response.status = ReportStatus.IN_PROGRESS.value
        return response, 202
    elif tech_spec.pdf_report_status is None:
        trigger_tech_spec_pdf_generation(project_info, tech_spec)
        response = PDFInProgressResponse()
        return response, 200

    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    url = generate_tech_spec_pdf_presigned_url(project_info, tech_spec, company_id)
    response = PDFReadyResponse()
    response.preSignedURL = url
    response.expiryInSeconds = DOCUMENT_EXPIRY_TIME
    return response, 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/document/approve", methods=["POST"])
@get_user_info
@flask_pydantic_response
def submit_tech_spec(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    """
    Submit and approve a technical specification document for code generation.

    This endpoint ensures idempotency by checking if a job of the same type is already
    in progress for the given project. Uses project-specific locks to prevent race
    conditions and maintains consistency across concurrent requests.

    :param user_info: User information from authentication
    :param project_id: Unique identifier for the project
    :param tech_spec_id: Unique identifier for the technical specification
    :return: Success response with status 200
    """
    with get_db_session() as session:
        project_info = get_project_by_user_id_with_relations(user_info["id"], project_id, session)
        if not project_info:
            raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

        tech_spec = get_tech_spec_by_project_id_and_id(project_id, tech_spec_id)
        if not tech_spec:
            raise TechnicalSpecificationNotFoundError(f"Failed to find tech spec with project id {project_id}")

        validate_subscription(user_info, session)
        validate_tech_spec_submit(project_info, tech_spec)
        validate_code_gen_submit(project_info)

        tech_spec_type = tech_spec.job_type

        # Check for job type-specific idempotency for code generation with appropriate locks
        lock_key = JOB_TYPE_TO_LOCK_KEY.get(tech_spec_type)
        if lock_key:
            with get_project_lock(project_id, lock_key):
                is_tech_spec_job_type_in_progress(project_info, tech_spec_type)

        if tech_spec_type == TechSpecJobType.NEW_PRODUCT:
            handle_new_product_tech_spec_submit(user_info, project_info, tech_spec, session)
        elif tech_spec_type == TechSpecJobType.ADD_FEATURE:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.ADD_FEATURE, BackpropCommand.ADD_FEATURE.value
            )
        elif tech_spec_type == TechSpecJobType.REFACTOR_CODE:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.REFACTOR_CODE, BackpropCommand.REFACTOR_CODE.value
            )
        elif tech_spec_type == TechSpecJobType.FIX_BUGS:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.FIX_BUGS, BackpropCommand.FIX_BUGS.value
            )
        elif tech_spec_type == TechSpecJobType.FIX_CVES:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.FIX_CVES, BackpropCommand.FIX_CVES.value
            )
        elif tech_spec_type == TechSpecJobType.ADD_TESTING:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.ADD_TESTING, BackpropCommand.ADD_TESTING.value
            )
        elif tech_spec_type == TechSpecJobType.DOCUMENT_CODE:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.DOCUMENT_CODE, BackpropCommand.DOCUMENT_CODE.value
            )
        elif tech_spec_type == TechSpecJobType.CUSTOM:
            handle_common_tech_spec_submit(
                user_info, project_info, tech_spec, session,
                ProjectRunType.CUSTOM, BackpropCommand.CUSTOM.value
            )
        else:
            raise JobTypeNotSupportedError(f"Tech spec type {tech_spec_type} is not for code generation.")

        update_project_timeline(project_id, session)
        session.commit()
    return Status200(message="Success"), 200


@tech_spec_bp.route("/<project_id>/tech-spec/prompt", methods=["POST"])
@validate_request(TechSpecPromptInput)
@get_user_info
@flask_pydantic_response
def post_tech_spec_prompt(user_info: Dict[str, Any], project_id: str, payload: TechSpecPromptInput):
    """
    Submit a technical specification prompt for processing.

    This endpoint ensures idempotency by preventing multiple jobs of the same type
    from running simultaneously for a given project. Uses project-specific locks
    and job type validation to maintain consistency.

    :param user_info: User information from authentication
    :param project_id: Unique identifier for the project
    :param payload: Technical specification prompt input containing job type and prompt
    :return: Success response with status 201
    """
    project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    process_tech_spec_request(user_info, project_info, payload)
    return Status201(message="Success"), 200


@tech_spec_bp.route("/<project_id>/tech-spec/<tech_spec_id>/run/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_run_status_by_tech_spec_id(user_info: Dict[str, Any], project_id: str, tech_spec_id: str):
    project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    if tech_spec_id == LATEST:
        tech_spec = get_latest_tech_spec_by_project_id(project_id)
        if not tech_spec:
            raise LatestTechSpecNotFoundError(f"Latest technical specification not found for project {project_id}")
        tech_spec_id = tech_spec.id

    tech_spec = get_project_run_by_project_and_tech_spec_id(tech_spec_id, project_id)
    if not tech_spec:
        return Status404(message="Job info not available"), 200
    response = map_to_model(tech_spec, GetTechnicalSpecJobStatusOutput)
    return response, 200


@tech_spec_bp.route("/<project_id>/tech-spec/sync/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_tech_spec_sync_status(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    logger.info(f"Project {project_id} is ready for sync.")
    if project_info.status != Status.GITHUB_COMPLETED:
        raise InvalidGithubRepoOnboardError(f"The project {project_id} is not ready for sync.")

    needs_scan = process_get_tech_spec_sync_status(user_info, project_info)
    sync_status = TechSpecSyncStatusOutput(needsScan=needs_scan)
    return sync_status, 200


@tech_spec_bp.route("/<project_id>/tech-spec/sync/status", methods=["POST"])
@get_user_info
@flask_pydantic_response
def post_tech_spec_sync_status(user_info: Dict[str, Any], project_id: str):
    """
    Trigger synchronization of technical specification with repository changes.

    This endpoint ensures idempotency by preventing multiple SYNC_TECH_SPEC jobs
    from running simultaneously for a given project. Uses project-specific locks
    and validates that no other sync operations are in progress.

    :param user_info: User information from authentication
    :param project_id: Unique identifier for the project
    :return: Success response with status 200
    """
    project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    logger.info(f"Project {project_id} is ready for sync.")
    code_gen = get_latest_code_gen_by_project_id(project_id)
    if code_gen:
        blitzy_commit = get_blitzy_commit_by_code_gen_id(code_gen.id)
        if blitzy_commit and blitzy_commit.status == BlitzyCommitStatus.PENDING:
            raise OperationNotSupportedError("Cannot submit technical specification request while PR is pending.")
        logger.info(f"Code generation job with id {code_gen.id} is ready for sync.")

    with get_project_lock(project_id, "sync_tech_spec"):
        process_post_tech_spec_sync_status(user_info, project_info)
    return Status200(message="Success"), 200


@tech_spec_bp.route("/<project_id>/tech-spec/revert", methods=["POST"])
@get_user_info
@flask_pydantic_response
def revert_tech_spec(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id_with_relations(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    revert_tech_spec_by_project_id(project_info)
    return Status200(message="Success"), 200


def process_tech_spec_request(user_info: Dict[str, Any], project_info: Project, payload: TechSpecPromptInput):
    """
    Process the tech spec request with idempotency guarantees.

    This function ensures that for any given project_id and TechSpecJobType combination,
    only one job can be in progress at a time. It performs both global job checks and
    job-type-specific checks with appropriate locking mechanisms.

    :param user_info: User info containing authentication and authorization details.
    :param project_info: Project info object containing project metadata.
    :param payload: User input payload containing job type and prompt details.
    """
    # Validate document submission for the free user.
    validate_document_submission(user_info["id"])

    # Check whether any tech spec job is in progress.
    is_tech_spec_job_in_progress(project_info)

    if payload.isDraft:
        handle_draft_request(project_info, payload)
        return

    handle_tech_spec_job_request(user_info, project_info, payload)


def is_tech_spec_job_in_progress(project_info: Project):
    project_id = project_info.id
    tech_spec = get_in_progress_tech_spec_by_project_id_and_status(project_id)
    if tech_spec:
        logger.warning(f"Tech spec job with id {tech_spec.id} is already in progress for project {project_id}")
        raise JobAlreadyRunningError("Cannot submit technical specification request while another is in progress.")


# Job type to lock key mapping for cleaner code
JOB_TYPE_TO_LOCK_KEY = {
    TechSpecJobType.ADD_FEATURE: "add_feature",
    TechSpecJobType.REFACTOR_CODE: "refactor_code",
    TechSpecJobType.SYNC_TECH_SPEC: "sync_tech_spec",
    TechSpecJobType.FIX_BUGS: "fix_bugs",
    TechSpecJobType.FIX_CVES: "fix_cves",
    TechSpecJobType.ADD_TESTING: "add_testing",
    TechSpecJobType.DOCUMENT_CODE: "document_code",
    TechSpecJobType.CUSTOM: "custom",
}


def _get_job_type_config(api_job_type: TechSpecJobTypeModel):
    """
    Get job type configuration mapping API model to database type, lock key, and handler.

    :param api_job_type: API model job type
    :return: Tuple of (db_job_type, lock_key, handler_func) or None if not supported
    """

    # Helper function to create common job type handler
    def make_common_handler(db_job_type, project_run_type):
        return lambda u, p, pl: tech_spec_handle_common_job_type(u, p, pl, db_job_type, project_run_type)

    job_type_configs = {
        TechSpecJobTypeModel.ADD_FEATURE: (
            TechSpecJobType.ADD_FEATURE,
            "add_feature",
            make_common_handler(TechSpecJobType.ADD_FEATURE, ProjectRunType.ADD_FEATURE)
        ),
        TechSpecJobTypeModel.REFACTOR_CODE: (
            TechSpecJobType.REFACTOR_CODE,
            "refactor_code",
            make_common_handler(TechSpecJobType.REFACTOR_CODE, ProjectRunType.REFACTOR_CODE)
        ),
        TechSpecJobTypeModel.FIX_BUGS: (
            TechSpecJobType.FIX_BUGS,
            "fix_bugs",
            make_common_handler(TechSpecJobType.FIX_BUGS, ProjectRunType.FIX_BUGS)
        ),
        TechSpecJobTypeModel.FIX_CVES: (
            TechSpecJobType.FIX_CVES,
            "fix_cves",
            make_common_handler(TechSpecJobType.FIX_CVES, ProjectRunType.FIX_CVES)
        ),
        TechSpecJobTypeModel.ADD_TESTING: (
            TechSpecJobType.ADD_TESTING,
            "add_testing",
            make_common_handler(TechSpecJobType.ADD_TESTING, ProjectRunType.ADD_TESTING)
        ),
        TechSpecJobTypeModel.DOCUMENT_CODE: (
            TechSpecJobType.DOCUMENT_CODE,
            "document_code",
            make_common_handler(TechSpecJobType.DOCUMENT_CODE, ProjectRunType.DOCUMENT_CODE)
        ),
        TechSpecJobTypeModel.CUSTOM: (
            TechSpecJobType.CUSTOM,
            "custom",
            make_common_handler(TechSpecJobType.CUSTOM, ProjectRunType.CUSTOM)
        ),
    }

    return job_type_configs.get(api_job_type)


def handle_tech_spec_job_request(user_info: Dict[str, Any], project_info: Project, payload: TechSpecPromptInput):
    """
    Handle tech spec job request with clean job type routing and idempotency.
    """
    # Job types that don't require existing project validation or idempotency checks
    if payload.type == TechSpecJobTypeModel.NEW_PRODUCT:
        tech_spec_handle_job_type_new_product(user_info, project_info, payload)
        return

    if payload.type == TechSpecJobTypeModel.EXISTING_PRODUCT:
        tech_spec_handle_job_type_existing_product(user_info, project_info, payload)
        return

    # All other job types require existing project validation and idempotency
    validate_existing_project_job_type(project_info, payload)

    # Map API model types to database types and handlers
    job_type_config = _get_job_type_config(payload.type)
    if not job_type_config:
        raise OperationNotSupportedError(f"Tech spec job type {payload.type} is not supported yet.")

    db_job_type, lock_key, handler_func = job_type_config

    # Execute with idempotency check
    with get_project_lock(project_info.id, lock_key):
        is_tech_spec_job_type_in_progress(project_info, db_job_type)
        handler_func(user_info, project_info, payload)


def handle_draft_request(project_info: Project, payload: TechSpecPromptInput):
    logger.warning(
        f"Draft technical specification request received for project {project_info.id} with type {payload.type}")
    raise OperationNotSupportedError("Draft technical specification request is not supported yet.")


def tech_spec_handle_job_type_new_product(user_info: Dict[str, Any], project_info: Project,
                                          payload: TechSpecPromptInput):
    """
    Handle the new product job type for a given project and payload.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    """
    tech_specs = get_all_tech_specs_by_project_id_order_by_created_at(project_info.id)

    # New product can be only created once at the start of the project.
    if len(tech_specs) > 0:
        logger.warning(f"Tech spec job type {payload.type} can be created only once and"
                       f" at the start for project {project_info.id}.")
        raise OperationNotSupportedError(
            f"Technical specification job type {payload.type} can be created only once at the start.")

    with get_db_session() as session:
        project_run = create_project_run_for_tech_spec(project_info.id, ProjectRunType.NEW_PRODUCT, session)
        _, updated_version = get_latest_tech_spec_with_new_version(project_info.id, session)
        # Save tech spec
        tech_spec = TechnicalSpec(
            project_id=project_info.id,
            prompt=payload.prompt,
            job_type=TechSpecJobType.NEW_PRODUCT,
            status=Status.QUEUED,
            version=updated_version,
        )

        session.add(tech_spec)
        session.flush()

        # Upload prompt to GCS.
        logger.info(f"Attempting to upload prompt to GCS for project {project_info.id}")
        upload_project_prompt_to_gcs(project_info, payload, project_info.repo_url, user_info)
        logger.info(f"Prompt uploaded to GCS for project {project_info.id}")

        # Trigger notification. Using update trigger.
        logger.info(f"Attempting to trigger document generation for project {project_info.id}")
        notification_payload = trigger_document_generation(user_info["id"], payload.prompt,
                                                           project_info.repo_url, 0, project_info.id,
                                                           project_run.id, tech_spec.id, user_info["company_id"])
        logger.info(f"Document generation triggered for project {project_info.id} with tech spec {tech_spec.id}")

        # Update tech spec with project run ID.
        logger.info(f"Updating project run with tech spec id{tech_spec.id}.")
        update_project_run_with_tech_spec_id(tech_spec.id, project_run.id, session)
        logger.info(f"Project run {project_run.id} updated with tech spec id {tech_spec.id}")

        tech_spec.job_metadata = notification_payload
        update_project_timeline(project_info.id, session)
        session.commit()
        logger.info("Tech spec request submitted successfully.")


def fetch_tech_spec_by_id_and_project_id(project_id: str, tech_spec_id: str) -> Optional[TechnicalSpecModel]:
    """
    Fetch technical specification by ID.
    :param project_id: Project ID.
    :param tech_spec_id: Tech spec ID.
    :return:
    """
    tech_spec = get_tech_spec_by_project_id_and_id(project_id, tech_spec_id)
    if not tech_spec:
        logger.warning(f"The technical spec with id {tech_spec_id} does not exist for project {project_id}.")
        return None
    return map_to_model(tech_spec, TechnicalSpecModel)


def get_tech_spec_by_project_id_and_id(project_id: str, tech_spec_id: str) -> TechnicalSpec:
    tech_spec = (get_latest_tech_spec_by_project_id(project_id)
                 if tech_spec_id == LATEST
                 else get_tech_spec_by_id_and_project_id(project_id, tech_spec_id))
    return tech_spec


def tech_spec_handle_job_type_existing_product(user_info: Dict[str, Any], project_info: Project,
                                               payload: TechSpecPromptInput):
    """
    Handle the existing product job type for a given project and payload.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    """
    tech_specs = get_all_tech_specs_by_project_id_order_by_created_at(project_info.id)

    # Existing product can be only created once at the start of the project.
    if len(tech_specs) > 0:
        logger.warning(f"Tech spec job type {payload.type} can be created only once and"
                       f" at the start for project {project_info.id}.")
        raise OperationNotSupportedError(
            f"Technical specification job type {payload.type} can be created only once at the start.")

    with get_db_session() as session:
        process_existing_product_job(user_info, project_info, payload, session)
        update_project_timeline(project_info.id, session)
        session.commit()
        logger.info(f"Attempting to upload prompt to GCS for project {project_info.id}")


def is_tech_spec_job_type_in_progress(project_info: Project, job_type: TechSpecJobType):
    """
    Check if a specific job type is already in progress for the given project.
    This ensures idempotency by preventing multiple jobs of the same type from running simultaneously.

    This function is used across multiple endpoints to ensure that for any given project_id and
    TechSpecJobType combination, only one job can be in progress at a time. This applies to:
    - POST /project/{project_id}/tech-spec/prompt
    - POST /project/{project_id}/tech-spec/{tech_spec_id}/document/approve
    - POST /project/{project_id}/tech-spec/sync/status

    :param project_info: Project info object containing the project ID and related metadata.
    :param job_type: The specific job type to check for idempotency. Supported types include:
        ADD_FEATURE, REFACTOR_CODE, SYNC_TECH_SPEC, FIX_BUGS, FIX_CVES, ADD_TESTING,
        DOCUMENT_CODE, CUSTOM.
    :raises JobAlreadyRunningError: If a job of the specified type is already in progress
        (status QUEUED or IN_PROGRESS) for the given project.
    """
    project_id = project_info.id
    tech_spec = get_in_progress_tech_spec_by_project_id_and_job_type(
        project_id, job_type
    )
    if tech_spec:
        logger.warning(
            f"Tech spec job with id {tech_spec.id} and type {job_type.value} is already in "
            f"progress for project {project_id}"
        )
        raise JobAlreadyRunningError(
            f"Cannot submit {job_type.value} request while another {job_type.value} job is in progress."
        )


def tech_spec_handle_job_type_add_feature(user_info: Dict[str, Any], project_info: Project,
                                          payload: TechSpecPromptInput):
    """
    Handle the add feature job type for a given project and payload.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    """
    tech_spec_handle_common_job_type(user_info, project_info, payload,
                                     TechSpecJobType.ADD_FEATURE, ProjectRunType.ADD_FEATURE)


def tech_spec_handle_job_type_refactor_code(user_info: Dict[str, Any], project_info: Project,
                                            payload: TechSpecPromptInput):
    """
    Handle the refactor code job type for a given project and payload.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    """
    tech_spec_handle_common_job_type(user_info, project_info, payload,
                                     TechSpecJobType.REFACTOR_CODE, ProjectRunType.REFACTOR_CODE)


def process_existing_product_job(user_info: Dict[str, Any], project_info: Project,
                                 payload: TechSpecPromptInput, session: Optional[Session] = None):
    """
    Submit the existing product job to the database and trigger the document generation.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    :param session: Client session if any.
    """
    with get_db_session(session) as session:
        project_run = create_project_run_for_tech_spec(project_info.id, ProjectRunType.EXISTING_PRODUCT, session)
        _, updated_version = get_latest_tech_spec_with_new_version(project_info.id, session)
        tech_spec = TechnicalSpec(
            project_id=project_info.id,
            prompt=payload.prompt,
            job_type=TechSpecJobType.EXISTING_PRODUCT,
            status=Status.QUEUED,
            version=updated_version,
        )

        session.add(tech_spec)
        session.flush()
        notification_payload = send_reverse_graph_existing_product_notification(project_info, project_run, user_info,
                                                                                tech_spec, session)

        tech_spec.job_metadata = notification_payload
        session.flush()

        # Update tech spec with project run ID.
        logger.info(f"Updating project run with tech spec id{tech_spec.id}.")
        update_project_run_with_tech_spec_id(tech_spec.id, project_run.id, session)
        logger.info(f"Project run {project_run.id} updated with tech spec id {tech_spec.id}")

        insert_tech_spec_document_context_record(notification_payload, session)
        session.commit()
        logger.info(f"Tech spec request submitted successfully for project {project_info.id}")


def tech_spec_handle_common_job_type(user_info: Dict[str, Any], project_info: Project, payload: TechSpecPromptInput,
                                     job_type: TechSpecJobType, run_type: ProjectRunType):
    """
    Handle common job type.
    :param user_info: User info.
    :param project_info: Project info.
    :param payload: User input payload.
    :param job_type: The TechSpecJobType enum value.
    :param run_type: The ProjectRunType enum value.
    """
    with get_db_session() as session:
        project_run = create_project_run_for_tech_spec(project_info.id, run_type, session)
        latest_tech_spec, updated_version = get_latest_tech_spec_with_new_version(project_info.id, session)
        logger.debug(f"Latest tech spec id: {latest_tech_spec.id}, Updated version: {updated_version}")
        tech_spec = TechnicalSpec(
            project_id=project_info.id,
            prompt=payload.prompt,
            job_type=job_type,
            status=Status.QUEUED,
            version=updated_version,
        )

        session.add(tech_spec)
        session.flush()
        notification_payload = send_generate_reverse_document_notification(
            project_info, project_run, user_info, tech_spec, latest_tech_spec, session
        )

        tech_spec.job_metadata = notification_payload
        logger.debug(f"Job metadata: {notification_payload}")
        session.flush()

        # Update tech spec with project run ID.
        logger.info(f"Updating project run with tech spec id{tech_spec.id}.")
        update_project_run_with_tech_spec_id(tech_spec.id, project_run.id, session)
        logger.info(f"Project run {project_run.id} updated with tech spec id {tech_spec.id}")

        insert_tech_spec_document_context_record(notification_payload, session)
        update_project_timeline(project_info.id, session)
        session.commit()
        logger.info(f"Tech spec request submitted successfully for project {project_info.id}")


def send_reverse_graph_existing_product_notification(project_info: Project, job: ProjectRun, user_info: Dict[str, Any],
                                                     tech_spec: TechnicalSpec,
                                                     session: Session) -> Dict[str, Any]:
    """
    Send a reverse graph notification for a given project and job.

    Payload for download-code notification:
    {
        "tech_spec_id": "",
        "repo_name": "estatekit-full",
        "repo_id": "9dd498ba-9534-4c8a-8863-f09aef16f0e9",
        "branch_id": "6c047c29-55c4-4b1c-b780-9842548fa92d",
        "branch_name": "trunk",
        "company_id": "9aa4c952-e6a8-481b-9059-03bc28a75c22",
        "user_id": "a2a2dba1-684a-4a48-83fc-5e38c32bbd65",
        "team_id": "e95ff329-43ba-4809-ad97-9afcc6133c3f",
        "job_id": "",
        "project_id": "",
        "head_commit_hash": "ab5c81525cd35dc95fff21c29820e42ae6d70202",
        "prev_head_commit_hash": "",
        "propagate": true
    }

    :param project_info: The project details for which the notification is to be sent.
    :param job: The job associated with the project that requires the notification.
    :param user_info: A dictionary containing information about the user who initiated
        the notification.
    :param tech_spec: The technical specifications needed to generate the notification.
    :param session: A session object used for maintaining context while processing.
    :return: None
    """
    tech_spec_payload = generate_technical_notification_payload(project_info, job, user_info, tech_spec, True, None,
                                                                session)
    # Validate and create branch lock.
    is_locked = has_active_lock(tech_spec_payload.branch_id, session)
    if is_locked:
        logger.warning(f"Branch pattern {tech_spec_payload.branch_id} is locked. Skipping onboarding.")
        raise BranchLockedError(f"Branch {tech_spec_payload.branch_name} is locked. Repo cannot be onboarded.")

    logger.info("Branch is not locked. Creating branch lock.")
    create_branch_lock(tech_spec_payload.branch_id, project_info, BranchLockReason.ONBOARDING_REPO, session)

    notification_payload = generate_tech_spec_notification_payload(tech_spec_payload)
    notification_payload["prev_head_commit_hash"] = ""

    publish_notification(publisher, notification_payload, PROJECT_ID, DOWNLOAD_CODE_TOPIC)
    logger.info(f"Sending reverse graph notification for project {project_info.id} with job {job.id}")
    return notification_payload


def generate_technical_notification_payload(project_info: Project, job: ProjectRun, user_info: Dict[str, Any],
                                            tech_spec: TechnicalSpec, propagate: bool,
                                            latest_tech_spec: TechnicalSpec = None,
                                            session: Session = None) -> TechSpecNotificationPayload:
    """
    Generates technical notification payload.
    :param project_info: Project object.
    :param job: Job object.
    :param user_info: User object.
    :param tech_spec: Technical spec object.
    :param propagate: Whether to propagate.
    :param latest_tech_spec: Latest technical spec if available.
    :param session: Client session if any.
    :return: Technical notification payload object.
    """
    source_github_repo_info = get_source_github_project_repo_by_id(project_info.id, session)
    if not source_github_repo_info:
        raise InvalidGithubRepoOnboardError(f"Source github repo info is not available for project {project_info.id}.")

    head_commit_hash = source_github_repo_info.current_commit_hash
    if not head_commit_hash:
        raise InvalidGithubRepoOnboardError(
            f"Head commit hash is not available for github repo {source_github_repo_info.repo_name}."
            f" Please onboard github repository again.")

    prev_head_commit_hash = source_github_repo_info.previous_commit_hash
    if not prev_head_commit_hash:
        raise InvalidGithubRepoOnboardError(
            f"Previous commit hash is not available for github repo {source_github_repo_info.repo_name}."
            f" Please onboard github repository again.")

    branch_pattern = get_branch_pattern_by_project_id(project_info.id, UsageType.SOURCE, session)
    user_team = get_team_member_by_user_id(user_info["id"], session)

    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    team_id = user_team.team_id if user_team and user_team.team_id else DEFAULT_NAME

    tech_spec_payload = TechSpecNotificationPayload(
        project_id=project_info.id,
        repo_name=branch_pattern.pattern.repo_name,
        head_commit_hash=head_commit_hash,
        prev_head_commit_hash=prev_head_commit_hash,
        repo_id=branch_pattern.pattern.repo_id,
        branch_id=branch_pattern.pattern.id,
        job_id=job.id,
        company_id=company_id,
        team_id=team_id,
        user_id=user_info["id"],
        tech_spec_id=tech_spec.id,
        branch_name=branch_pattern.pattern.branch_name,
        propagate=propagate,
        document_mode=BackpropChangeMode.GENERATE.value,
        job_type=(
            tech_spec.job_type.value
            if tech_spec.job_type not in [TechSpecJobType.SYNC_TECH_SPEC, TechSpecJobType.EXISTING_PRODUCT]
            else None
        ),
        git_project_repo_id=source_github_repo_info.id
    )

    if latest_tech_spec:
        tech_spec_payload.previous_tech_spec_id = latest_tech_spec.id

    return tech_spec_payload


def generate_tech_spec_notification_payload(notification_payload: TechSpecNotificationPayload) -> Dict[str, Any]:
    """
    Generates reverse gra
    :param notification_payload: Technical notification payload object.
    :return: Notification payload dictionary.
    """
    payload = {
        "tech_spec_id": notification_payload.tech_spec_id,
        "repo_name": notification_payload.repo_name,
        "repo_id": notification_payload.repo_id,
        "branch_id": notification_payload.branch_id,
        "branch_name": notification_payload.branch_name,
        "company_id": notification_payload.company_id,
        "user_id": notification_payload.user_id,
        "team_id": notification_payload.team_id,
        "job_id": notification_payload.job_id,
        "project_id": notification_payload.project_id,
        "head_commit_hash": notification_payload.head_commit_hash,
        "prev_head_commit_hash": notification_payload.prev_head_commit_hash,
        "propagate": notification_payload.propagate,
        "git_project_repo_id": notification_payload.git_project_repo_id,
    }

    # Only include job_type if it's not None
    if notification_payload.job_type is not None:
        payload["job_type"] = notification_payload.job_type

    if notification_payload.previous_tech_spec_id:
        payload["previous_tech_spec_id"] = notification_payload.previous_tech_spec_id

    return payload


def send_generate_reverse_document_notification(project_info: Project, job: ProjectRun,
                                                user_info: Dict[str, Any],
                                                tech_spec: TechnicalSpec,
                                                latest_tech_spec: TechnicalSpec,
                                                session: Session):
    """
    Uploads prompt to GCS if available and sends a notification to generate a reverse document. Currently this method
    supports two types of tech spec jobs ADD_FEATURE and REFACTOR_CODE.

    Payload for generate-reverse-document notification:
    {
        "tech_spec_id": "",
        // Github repo name.
        "repo_name": "estatekit-full",
        "repo_id": "9dd498ba-9534-4c8a-8863-f09aef16f0e9",
        "branch_id": "6c047c29-55c4-4b1c-b780-9842548fa92d",
        "project_id": "",
        "job_id": "",
        "propagate": false,
        "user_id": "a2a2dba1-684a-4a48-83fc-5e38c32bbd65",
        "company_id": "9aa4c952-e6a8-481b-9059-03bc28a75c22",
        "team_id": "e95ff329-43ba-4809-ad97-9afcc6133c3f",
        "head_commit_hash": "ab5c81525cd35dc95fff21c29820e42ae6d70202",
        'document_mode': "UPDATE"
    },

    :param project_info: Contains information about the project for which the reverse document
        notification is being generated.
    :param job: Represents the job context under which the notification is created.
    :param user_info: Dictionary containing user-related information needed
        for constructing the notification.
    :param tech_spec: Object holding the technical specifications required for
        document generation.
    :param latest_tech_spec: Latest technical spec if available.
    :param session: Database or API session object used to access and manipulate
        data necessary for payload generation.
    :return: None. The function sends out the constructed notification but does
        not return any value.
    """
    tech_spec_payload = generate_technical_notification_payload(project_info, job, user_info, tech_spec, False,
                                                                latest_tech_spec, session)
    notification_payload = generate_tech_spec_notification_payload(tech_spec_payload)
    notification_payload["document_mode"] = "UPDATE"

    if tech_spec.prompt:
        # Upload input prompt to GCS.
        logger.info(f"Attempting to upload prompt to GCS for project {project_info.id}")
        upload_tech_spec_prompt_to_gcs(project_info, notification_payload, tech_spec.prompt)
        logger.info(f"Prompt uploaded to GCS for project {project_info.id}")

    publish_notification(publisher, notification_payload, PROJECT_ID, GENERATE_REVERSE_DOCUMENT_TOPIC)
    return notification_payload


def validate_existing_project_job_type(project_info: Project, payload: TechSpecPromptInput):
    """
    Validate existing project for job type add feature and refactor code.
    :param project_info: Project info.
    :param payload: User input.
    """
    tech_specs = get_all_tech_specs_by_project_id_order_by_created_at(project_info.id)

    # Existing product can be only created once at the start of the project.
    if not len(tech_specs) > 0:
        logger.warning(f"Cannot directly trigger tech spec job type {payload.type} for project {project_info.id}."
                       f" Please trigger ADD_PRODUCT or EXISTING_PRODUCT first.")
        raise OperationNotSupportedError(
            f"Cannot directly trigger tech spec job type {payload.type} for project {project_info.id}."
            f" Please trigger ADD_PRODUCT or EXISTING_PRODUCT first.")


def create_project_run_for_tech_spec(project_id: str, run_type: ProjectRunType, session: Session = None) -> ProjectRun:
    """
    Creates a project run job for a technical specification.
    :param project_id: Project ID.
    :param run_type: Run type.
    :param session: Client session.
    :return: Project run object.
    """
    project_run = ProjectRun(
        status=ProjectRunStatus.QUEUED,
        project_id=project_id,
        stage=ProjectRunStage.TECH_SPEC,
        run_type=run_type,
        start_at=datetime.utcnow()
    )

    project_run = save_project_run(project_run, session)
    return project_run


def process_get_tech_spec_sync_status(user_info: Dict[str, Any], project_info: Project,
                                      session: Optional[Session] = None) -> bool:
    """
    Processes and determines the synchronization status of a Git repository for a
    given project. It compares the current commit hash with the previous commit
    hash stored in the project repository information. If the commit hashes differ,
    it updates the synchronization status and sets a scan flag for the repository.

    :param user_info: A dictionary containing information about the user, including
        user-specific details like user ID and permissions.
        Type: Dict[str, Any]
    :param project_info: An instance of the `Project` class containing details about
        the project to be processed, including the associated GitHub repository.
        Type: Project
    :param session: Client session if any.
    :return: A boolean value indicating whether the synchronization status was
        updated or skipped. Returns True if the status was updated, otherwise False.
        Type: bool
    """
    source_github_repo_info = get_source_github_project_repo_by_id(project_info.id)

    if not source_github_repo_info:
        raise InvalidGithubRepoOnboardError(f"Source github repo info is not available for project {project_info.id}.")

    logger.info(f"[COMMIT_TRACKING] GET Sync Status - Project {project_info.id} - Initial state: "
                f"needs_scan={source_github_repo_info.needs_scan}, "
                f"previous_commit_hash={source_github_repo_info.previous_commit_hash}, "
                f"current_commit_hash={source_github_repo_info.current_commit_hash}, "
                f"github_current_commit_hash={source_github_repo_info.github_current_commit_hash}")

    if source_github_repo_info.current_commit_hash == source_github_repo_info.previous_commit_hash:
        logger.warning(f"[COMMIT_TRACKING] GET Sync Status - Potential issue detected: current_commit_hash"
                       f" equals previous_commit_hash ({source_github_repo_info.current_commit_hash})")

    org_name = source_github_repo_info.org_name
    repo_id = source_github_repo_info.repo_id
    branch_name = source_github_repo_info.branch_name


    github_commit_info = get_branch_head_commit_from_github_handler(user_info["id"], org_name, repo_id,
                                                                    branch_name)

    if not github_commit_info:
        raise GitHubIntegrationError(f"GitHub commit info is not available for repo {repo_id}.")

    logger.info(f"head_commit_info: {github_commit_info}")

    current_github_commit_hash = github_commit_info.get("commit", {}).get("sha")

    logger.info(f"Comparing commit hashes: stored={source_github_repo_info.current_commit_hash}, "
                f"from_github={current_github_commit_hash}")

    needs_scan = False
    if source_github_repo_info.current_commit_hash == current_github_commit_hash:
        logger.info(
            f"Current commit hash {current_github_commit_hash} is same as "
            f"stored commit hash {source_github_repo_info.current_commit_hash}. Skipping sync status.")
        return needs_scan

    logger.info(f"Different commit hash found. Current: {source_github_repo_info.current_commit_hash}, "
                f"GitHub: {current_github_commit_hash}. Updating sync status.")

    needs_scan = True
    update_github_project_repo = {
        GitHubProjectRepo.github_current_commit_hash: current_github_commit_hash,
        GitHubProjectRepo.needs_scan: needs_scan,
    }

    with get_db_session(session) as session:
        updated_count = update_github_project_by_project_id(project_info.id, update_github_project_repo, session)
        session.commit()
        logger.info(f"Updated count for repo {source_github_repo_info.repo_id} is {updated_count}")
        logger.info(f"Updated sync status for project {project_info.id}. needs_scan=True, "
                    f"github_current_commit_hash={current_github_commit_hash}")
        return needs_scan


def process_post_tech_spec_sync_status(user_info: Dict[str, Any], project_info: Project):
    """
    Processes the synchronization status of a technical specification for a given
    project with idempotency guarantees. This function manages the synchronization
    between the system and the source GitHub repository, ensuring that the technical
    specifications are up to date.

    This function ensures idempotency by:
    1. Checking if any tech spec job is already in progress for the project
    2. Specifically checking if a SYNC_TECH_SPEC job is already running
    3. Using project-specific locks to prevent race conditions

    If the repository does not require synchronization, an appropriate exception is
    raised. Otherwise, the function triggers the synchronization job and handles
    related operations, including uploading the prompt to GCS.

    :param user_info: Contains information about the user initiating the request.
        Expected to include user identification and metadata.
    :param project_info: An object that stores details about the project,
        including its unique identifier and related metadata. The type of this
        object provides additional interfaces for interacting with the project.
    :return: None. All updates and synchronization tasks are performed as side
        effects, either in the database or via external systems.
    :raises JobAlreadyRunningError: If a SYNC_TECH_SPEC job is already in progress.
    :raises GithubRepoAlreadyInSync: If no new changes are detected to sync.
    """
    # Check whether any tech spec job is in progress.
    is_tech_spec_job_in_progress(project_info)

    # Check for SYNC_TECH_SPEC job type-specific idempotency
    is_tech_spec_job_type_in_progress(project_info, TechSpecJobType.SYNC_TECH_SPEC)

    with get_db_session() as session:
        needs_scan = process_get_tech_spec_sync_status(user_info, project_info, session)
        if not needs_scan:
            logger.info(f"Skipping sync status for project {project_info.id} because it is already synced.")
            raise GithubRepoAlreadyInSync("No new changes detected to sync")

        source_github_repo_info = get_source_github_project_repo_by_id(project_info.id, session)
        logger.info(f"[COMMIT_TRACKING] POST Sync Status - Project {project_info.id} - Initial state: "
                    f"needs_scan={source_github_repo_info.needs_scan}, "
                    f"previous_commit_hash={source_github_repo_info.previous_commit_hash}, "
                    f"current_commit_hash={source_github_repo_info.current_commit_hash}, "
                    f"github_current_commit_hash={source_github_repo_info.github_current_commit_hash}")

        if source_github_repo_info.github_current_commit_hash == source_github_repo_info.current_commit_hash:
            logger.warning("No new changes to sync - github_current_commit_hash matches current_commit_hash")
            raise GithubRepoAlreadyInSync("No new changes detected to sync")

        process_sync_tech_spec_job(user_info, project_info, session)
        source_github_repo_info.needs_scan = False

        logger.info(f"[COMMIT_TRACKING] POST Sync Status - About to update commit hashes: "
                    f"previous_commit_hash={source_github_repo_info.previous_commit_hash}, "
                    f"current_commit_hash={source_github_repo_info.current_commit_hash} -> "
                    f"github_current_commit_hash={source_github_repo_info.github_current_commit_hash}")

        if source_github_repo_info.github_current_commit_hash != source_github_repo_info.previous_commit_hash:
            source_github_repo_info.previous_commit_hash = source_github_repo_info.current_commit_hash
            logger.info(f"Updated previous_commit_hash to {source_github_repo_info.previous_commit_hash}")

        source_github_repo_info.current_commit_hash = source_github_repo_info.github_current_commit_hash
        logger.info(f"[COMMIT_TRACKING] POST Sync Status - Final state: "
                    f"previous_commit_hash={source_github_repo_info.previous_commit_hash}, "
                    f"current_commit_hash={source_github_repo_info.current_commit_hash}, "
                    f"github_current_commit_hash={source_github_repo_info.github_current_commit_hash}")

        update_project_timeline(project_info.id, session)
        session.commit()
        logger.info(f"Attempting to upload prompt to GCS for project {project_info.id}")


def process_sync_tech_spec_job(user_info: Dict[str, Any], project_info: Project, session: Optional[Session] = None):
    """
    Submit the sync tech spec job to the database and trigger the document generation.
    :param user_info: User info.
    :param project_info: Project info.
    :param session: Client session if any.
    """
    with get_db_session(session) as session:
        project_run = create_project_run_for_tech_spec(project_info.id, ProjectRunType.EXISTING_PRODUCT, session)
        latest_tech_spec, updated_version = get_latest_tech_spec_with_new_version(project_info.id, session)
        tech_spec = TechnicalSpec(
            project_id=project_info.id,
            prompt="",
            job_type=TechSpecJobType.SYNC_TECH_SPEC,
            status=Status.QUEUED,
            version=updated_version,
        )

        session.add(tech_spec)
        session.flush()
        notification_payload = send_sync_tech_spec_reverse_graph_notification(
            project_info, project_run, user_info, tech_spec, latest_tech_spec, session
        )

        tech_spec.job_metadata = notification_payload
        session.flush()

        # Update tech spec with project run ID.
        logger.info(f"Updating project run with tech spec id{tech_spec.id}.")
        update_project_run_with_tech_spec_id(tech_spec.id, project_run.id, session)
        logger.info(f"Project run {project_run.id} updated with tech spec id {tech_spec.id}")

        insert_tech_spec_document_context_record(notification_payload, session)
        logger.info(f"Tech spec request submitted successfully for project {project_info.id}")


def generate_sync_technical_notification_payload(project_info: Project, job: ProjectRun, user_info: Dict[str, Any],
                                                 tech_spec: TechnicalSpec, propagate: bool,
                                                 latest_tech_spec: TechnicalSpec = None,
                                                 session: Session = None) -> TechSpecNotificationPayload:
    """
    Generates sync technical notification payload.
    :param project_info: Project object.
    :param job: Job object.
    :param user_info: User object.
    :param tech_spec: Technical spec object.
    :param propagate: Whether to propagate.
    :param latest_tech_spec: Latest technical spec if available.
    :param session: Client session if any.
    :return: Technical notification payload object.
    """
    source_github_repo_info = get_source_github_project_repo_by_id(project_info.id, session)

    logger.info(f"Generating sync notification with commit hashes: "
                f"previous={source_github_repo_info.previous_commit_hash}, "
                f"current={source_github_repo_info.current_commit_hash}, "
                f"github_current={source_github_repo_info.github_current_commit_hash}")

    if source_github_repo_info.github_current_commit_hash == source_github_repo_info.current_commit_hash:
        logger.warning("Commit hashes match in generate_sync_technical_notification_payload - potential data issue")

    if source_github_repo_info.current_commit_hash == source_github_repo_info.previous_commit_hash:
        logger.warning("Current and previous commit hashes are identical - potential data issue")

    branch_pattern = get_branch_pattern_by_project_id(project_info.id, UsageType.SOURCE, session)
    user_team = get_team_member_by_user_id(user_info["id"], session)

    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    team_id = user_team.team_id if user_team and user_team.team_id else DEFAULT_NAME

    tech_spec_payload = TechSpecNotificationPayload(
        project_id=project_info.id,
        repo_name=branch_pattern.pattern.repo_name,
        head_commit_hash=source_github_repo_info.github_current_commit_hash,
        prev_head_commit_hash=source_github_repo_info.current_commit_hash,
        repo_id=branch_pattern.pattern.repo_id,
        branch_id=branch_pattern.pattern.id,
        job_id=job.id,
        company_id=company_id,
        team_id=team_id,
        user_id=user_info["id"],
        tech_spec_id=tech_spec.id,
        branch_name=branch_pattern.pattern.branch_name,
        propagate=propagate,
        document_mode=BackpropChangeMode.GENERATE.value,
        git_project_repo_id=source_github_repo_info.id
    )

    if latest_tech_spec:
        tech_spec_payload.previous_tech_spec_id = latest_tech_spec.id

    return tech_spec_payload


def send_sync_tech_spec_reverse_graph_notification(project_info: Project, job: ProjectRun,
                                                   user_info: Dict[str, Any],
                                                   tech_spec: TechnicalSpec,
                                                   latest_tech_spec: TechnicalSpec,
                                                   session: Session) -> Dict[str, Any]:
    """
    Send a sync tech spec reverse graph notification for a given project and job.

    :param project_info: The project details for which the notification is to be sent.
    :param job: The job associated with the project that requires the notification.
    :param user_info: A dictionary containing information about the user who initiated
        the notification.
    :param tech_spec: The technical specifications needed to generate the notification.
    :param latest_tech_spec: Latest technical spec.
    :param session: A session object used for maintaining context while processing.
    :return: None
    """
    tech_spec_payload = generate_sync_technical_notification_payload(project_info, job, user_info, tech_spec, True,
                                                                     latest_tech_spec, session)

    # Validate and create branch lock.
    is_locked = has_active_lock(tech_spec_payload.branch_id, session)
    if is_locked:
        logger.warning(f"Branch pattern {tech_spec_payload.branch_id} is locked. Skipping onboarding.")
        raise BranchLockedError(f"Branch {tech_spec_payload.branch_name} is locked. Repo cannot be onboarded.")

    logger.info("Branch is not locked. Creating branch lock.")
    create_branch_lock(tech_spec_payload.branch_id, project_info, BranchLockReason.ONBOARDING_REPO, session)

    notification_payload = generate_tech_spec_notification_payload(tech_spec_payload)

    publish_notification(publisher, notification_payload, PROJECT_ID, DOWNLOAD_CODE_TOPIC)
    logger.info(f"Sending sync tech spec reverse graph notification for project {project_info.id} with job {job.id}")
    return notification_payload


@tech_spec_bp.route("/<project_id>/tech-spec/parse-markdown", methods=["POST"])
@get_user_info
@validate_request(ParseMarkdownRequest)
@flask_pydantic_response
def parse_markdown_to_json(project_id: str, user_info: dict, request_data: ParseMarkdownRequest) -> ParseMarkdownResponse:
    """
    Parse markdown technical specification to JSON hierarchy.

    Args:
        project_id: Project identifier
        user_info: User information from authentication
        request_data: Request containing markdown content

    Returns:
        ParseMarkdownResponse with structured sections
    """
    try:
        logger.info(f"Parsing markdown to JSON for project {project_id}")

        # Validate user access to project
        with get_db_session() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ResourceNotFound(f"Project {project_id} not found")

            # Check user access (simplified - you may want more sophisticated access control)
            user = session.query(User).filter(User.user_id == user_info["user_id"]).first()
            if not user:
                raise UnauthorizedError("User not found")

        # Validate markdown content
        if not validate_markdown_content(request_data.markdown_content):
            raise InvalidInput("Invalid markdown content provided")

        # Parse markdown to JSON
        parser = TechSpecParser()
        result = parser.parse_markdown_to_json(
            markdown_content=request_data.markdown_content,
            document_title=request_data.document_title
        )

        logger.info(f"Successfully parsed markdown to JSON for project {project_id}")
        return result

    except (ResourceNotFound, UnauthorizedError, InvalidInput) as e:
        logger.warning(f"Client error in parse_markdown_to_json: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error parsing markdown to JSON for project {project_id}: {str(e)}")
        raise OperationFailedError(f"Failed to parse markdown: {str(e)}")


@tech_spec_bp.route("/<project_id>/tech-spec/parse-json", methods=["POST"])
@get_user_info
@validate_request(ParseJsonRequest)
@flask_pydantic_response
def parse_json_to_markdown(project_id: str, user_info: dict, request_data: ParseJsonRequest) -> ParseJsonResponse:
    """
    Parse JSON hierarchy to markdown technical specification.

    Args:
        project_id: Project identifier
        user_info: User information from authentication
        request_data: Request containing JSON structure

    Returns:
        ParseJsonResponse with markdown content
    """
    try:
        logger.info(f"Parsing JSON to markdown for project {project_id}")

        # Validate user access to project
        with get_db_session() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ResourceNotFound(f"Project {project_id} not found")

            # Check user access (simplified - you may want more sophisticated access control)
            user = session.query(User).filter(User.user_id == user_info["user_id"]).first()
            if not user:
                raise UnauthorizedError("User not found")

        # Validate JSON structure
        if not validate_json_structure(request_data.sections):
            raise InvalidInput("Invalid JSON structure provided")

        # Parse JSON to markdown
        parser = TechSpecParser()
        result = parser.parse_json_to_markdown(
            document_title=request_data.document_title,
            sections=request_data.sections
        )

        logger.info(f"Successfully parsed JSON to markdown for project {project_id}")
        return result

    except (ResourceNotFound, UnauthorizedError, InvalidInput) as e:
        logger.warning(f"Client error in parse_json_to_markdown: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error parsing JSON to markdown for project {project_id}: {str(e)}")
        raise OperationFailedError(f"Failed to parse JSON: {str(e)}")
