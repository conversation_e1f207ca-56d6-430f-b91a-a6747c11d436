from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, List
from firebase_admin import auth
from firebase_admin.auth import UserRecord
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (
    GeoLocation,
    Subscription,
    SubscriptionType,
    User,
    UserConfig,
    Team,
    TeamMember
)

from src.api.models import (
    CreateUserInput,
    UserSearchQuery,
    UserSearchModel,
    UserSummaryModel,
    UserTeamMembershipModel, TeamRole
)
from src.error.errors import (
    InvalidUserCredentials,
    UserUpdateError,
    TeamNotFoundError,
    UserValidationError
)
from src.service.ip_service import (
    add_geolocation,
    get_client_ip,
    get_geolocation_from_ip
)
from src.service.subscription_service import save_subscription
from src.service.user_config_service import save_config


def create_user_using_firebase_user_id(user_payload: CreateUserInput, session: Session) -> Dict[str, Any]:
    """
    Creates user with default subscription. Uses firebase to fetch information. User gets created with default
    subscription.
    :param session: User session.
    :param user_payload: User payload received via API.
    :return: User created in database.
    """

    user_info = fetch_user_info_from_auth_provider(user_payload.userId)
    if user_info.email != user_payload.email:
        raise InvalidUserCredentials(
            f"Invalid credentials provided. Got {user_payload.email} but expected {user_info.email}")

    user = User(
        user_id=user_payload.userId,
        email=str(user_payload.email),
        is_verified=True,
        registration_completed=False,
    )
    display_name = user_info.display_name
    if display_name:
        name_parts = display_name.split()
        first_name = name_parts[0] if name_parts else ""
        last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
        user.first_name = first_name
        user.last_name = last_name

    session.add(user)
    session.flush()

    subscription = Subscription(
        user_id=user.id,
        plan_name=SubscriptionType.FREE,
        start_date=datetime.utcnow(),
    )
    save_subscription(subscription, session)

    config = UserConfig(
        user_id=user.id,
        tech_spec_notification_enabled=True,
        code_gen_notification_enabled=True,
        platform_config={}
    )
    save_config(config, session)
    return user.to_dict()


def create_user(user: User, session: Optional[Session] = None) -> User:
    """
    Create and persist a new user in the database.

    :param user: User object to be created and added to the database.
    :type user: User
    :param session: Optional database session to be reused. If not provided, a new
        session is created and managed internally.
    :type session: Optional[Session]
    :return: The newly created User object.
    :rtype: User
    """
    with get_db_session(session) as session:
        session.add(user)
        session.flush()
        return user


def fetch_user_info_from_auth_provider(user_id: str) -> UserRecord:
    """
    Fetches User from auth provider.
    :param user_id: User ID.
    :return: User ID fetched from auth provider.
    """
    user_info = auth.get_user(user_id)
    if not user_info:
        raise Exception(f"User with ID {user_id} does not exists in firebase.")
    return user_info


def get_user_by_id(user_id: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user information by ID.
    :param user_id: User ID.
    :param session: Session if any.
    :return: User if found.
    """
    with get_db_session(session) as session:
        user = (session.query(User).options(joinedload(User.subscription),
                                            joinedload(User.user_config)).filter(User.id == user_id).first())

        if user and not session:
            session.expunge(user)
        return user


def update_user_by_id(user_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update user information by ID.
    :param user_id: User ID.
    :param update_payload: Update payload.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        user_updated = session.query(User).filter(
            User.id == user_id,
        ).update(update_payload)

        if not user_updated:
            logger.error(f"Failed to update user {user_id}")
            raise UserUpdateError(f"Failed to update user information for ID {user_id}")


def add_user(user: User, session: Optional[Session] = None) -> User:
    """
    Add user to the database.
    :param user: User model.
    :param session: Session if any.
    :return: Saved model.
    """
    with get_db_session(session) as session:
        session.add(user)
        session.flush()
        return user


def get_user_by_user_email(email: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user information by email.
    :param email: Email ID.
    :param session: Session if any.
    :return: User object if found.
    """
    with get_db_session(session) as session:
        user = (session.query(User).options(joinedload(User.subscription)).filter(User.email == email).first())

        if user and not session:
            session.expunge(user)
        return user


def save_user_geolocation(user_id: str, session: Session) -> None:
    """
    Helper function to save user geolocation if it doesn't exist.
    Args:
        user_id: User ID from the database
        session: Active database session
    """
    existing_geolocation = session.query(GeoLocation).filter(
        GeoLocation.user_id == user_id
    ).first()
    if not existing_geolocation:
        logger.info(f"No existing geolocation found for user_id: {user_id}")
        ip_address = get_client_ip()
        if ip_address:
            geo_data = get_geolocation_from_ip(ip_address)
            if geo_data:
                geolocation = GeoLocation(
                    user_id=user_id,
                    ip_address=geo_data['ip'],
                    country_code=geo_data['country_code'],
                    city=geo_data['city'],
                    region=geo_data['region'],
                    latitude=geo_data['latitude'],
                    longitude=geo_data['longitude'],
                    timezone=geo_data['timezone']
                )
                add_geolocation(geolocation, session)
                session.commit()
            else:
                logger.warning(
                    message=f"Failed to get geolocation information for user_id: {user_id}"
                )


def find_users_service(payload: UserSearchQuery, session: Optional[Session] = None) -> UserSearchModel:
    """
    Find users based on search criteria with pagination.
    :param payload: UserSearchQuery containing search filters and pagination params.
    :param session: Client session if any.
    :return: UserSearchModel object containing matching users and pagination info.
    """
    with get_db_session(session) as session:
        try:
            # Start building the base query
            query = session.query(User).filter(User.is_deleted == False)

            # Apply company filter
            if payload.company_id:
                query = query.filter(User.company_id == payload.company_id)

            # Apply team filter
            if payload.team_id:
                # Verify team exists
                team = session.query(Team).filter(Team.id == payload.team_id).first()
                if not team:
                    logger.error(f"Team not found for user search: {payload.team_id}")
                    raise TeamNotFoundError(payload.team_id)

                # Join with team members to filter by team membership
                query = query.join(TeamMember, User.id == TeamMember.user_id).filter(
                    and_(
                        TeamMember.team_id == payload.team_id,
                        TeamMember.is_deleted == False
                    )
                )

            # Apply search term filter
            if payload.search and payload.search.strip():
                search_term = f"%{payload.search.strip()}%"
                query = query.filter(
                    or_(
                        func.lower(User.first_name).like(search_term.lower()),
                        func.lower(User.last_name).like(search_term.lower()),
                        func.lower(User.email).like(search_term.lower())
                    )
                )

            # Get total count before applying pagination
            total_count = query.count()

            # Apply pagination
            limit = min(payload.limit or 20, 100)  # Enforce max limit of 100
            offset = payload.offset or 0

            # Execute query with pagination and ordering
            users = (
                query
                .order_by(User.first_name, User.last_name, User.email)
                .offset(offset)
                .limit(limit)
                .all()
            )

            # Convert to UserSummaryModel
            user_models = []
            for user in users:
                # Get team memberships for this user
                team_memberships = _get_user_team_memberships(user.id, session)

                user_model = UserSummaryModel(
                    id=user.id,
                    email=user.email,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    avatar_url=user.avatar_blob,
                    teams=team_memberships,
                )
                user_models.append(user_model)

            result = UserSearchModel(
                users=user_models,
                total_count=total_count,
            )

            logger.info(f"Found {len(user_models)} users (total: {total_count}) with search criteria")
            return result

        except Exception as e:
            logger.error(f"Error searching users: {str(e)}")
            # Re-raise known exceptions
            if isinstance(e, (TeamNotFoundError, UserValidationError)):
                raise
            # Wrap unexpected exceptions
            raise UserValidationError(f"Failed to search users: {str(e)}")


def _get_user_team_memberships(user_id: str, session: Session) -> List[UserTeamMembershipModel]:
    """
    Get team memberships for a specific user.
    :param user_id: User ID to get team memberships for.
    :param session: Database session.
    :return: List of UserTeamMembershipModel objects.
    """
    try:
        # Query team memberships with team details
        memberships = (
            session.query(TeamMember, Team)
            .join(Team, TeamMember.team_id == Team.id)
            .filter(
                and_(
                    TeamMember.user_id == user_id,
                    TeamMember.is_deleted == False,
                    Team.is_deleted == False
                )
            )
            .all()
        )

        membership_models = []
        for team_member, team in memberships:
            # Get member count for this team
            member_count = (
                session.query(func.count(TeamMember.id))
                .filter(
                    and_(
                        TeamMember.team_id == team.id,
                        TeamMember.is_deleted == False
                    )
                )
                .scalar()
            )

            raw_role = getattr(team_member, "role", None)
            try:
                role = TeamRole(raw_role.value if isinstance(raw_role, Enum) else raw_role)
            except ValueError:
                role = TeamRole.MEMBER  # fallback

            membership_model = UserTeamMembershipModel(
                team_id=team.id,
                team_name=team.name,
                role=role,
                joined_at=team_member.joined_at,
                member_count=member_count
            )
            membership_models.append(membership_model)

        return membership_models

    except Exception as e:
        logger.warning(f"Error getting team memberships for user {user_id}: {str(e)}")
        return []  # Return empty list if team membership query fails


