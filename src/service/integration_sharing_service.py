from typing import Optional
import logging
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from datetime import datetime, timezone

from common_models.db_client import get_db_session
from common_models.models import (
    GithubInstallation,
    Team,
    Company,
    User,
    GitHubInstallationAccess,
    TeamMember
)
from src.api.models import (
    IntegrationType,
    IntegrationTeamsModel,
    IntegrationListModel,
    IntegrationModel,
    TeamModel
)
from src.error.errors import (
    TeamNotFoundError,
    UserValidationError,
    DatabaseUpdateError,
    ResourceNotFound
)

logger = logging.getLogger(__name__)


def list_integration_teams_service(
        integration_type: IntegrationType,
        session: Optional[Session] = None
) -> IntegrationTeamsModel:
    """
    List teams with an integration type.
    :param integration_type: Integration type.
    :param session: Client session if any.
    :return: IntegrationTeamsModel object.
    """
    with get_db_session(session) as session:
        try:
            # Get all active integrations of the specified type
            integrations = (
                session.query(GithubInstallation)
                .filter(
                    and_(
                        GithubInstallation.svc_type == integration_type.value,
                        GithubInstallation.is_deleted == False,
                        GithubInstallation.status.in_(['ACTIVE', 'PENDING'])
                    )
                )
                .all()
            )

            if not integrations:
                return IntegrationTeamsModel(
                    integration_type=integration_type,
                    teams=[],
                )

            # Get teams that have access to these integrations
            integration_ids = [integration.id for integration in integrations]

            # Query teams with access grants
            team_ids_with_access = (
                session.query(GitHubInstallationAccess.entity_id)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id.in_(integration_ids),
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .distinct()
                .subquery()
            )

            # Then get the full Team objects
            teams_with_access = (
                session.query(Team)
                .filter(
                    and_(
                        Team.id.in_(team_ids_with_access),
                        Team.is_deleted == False
                    )
                )
                .all()
            )
            # Get unique team IDs first (avoid DISTINCT on JSONB)
            team_ids_with_shared = (
                session.query(Team.id)
                .join(User, Team.company_id == User.company_id)
                .join(
                    GithubInstallation,
                    and_(
                        GithubInstallation.user_id == User.id,
                        GithubInstallation.id.in_(integration_ids),
                        GithubInstallation.is_shared == True
                    )
                )
                .filter(Team.is_deleted == False)
                .distinct()
                .subquery()
            )

            # Then get the full Team objects
            teams_with_shared = (
                session.query(Team)
                .filter(
                    and_(
                        Team.id.in_(team_ids_with_shared),
                        Team.is_deleted == False
                    )
                )
                .all()
            )

            # Combine and deduplicate teams
            all_teams = {}

            # Add teams with explicit access grants
            for team in teams_with_access:
                if team.id not in all_teams:
                    all_teams[team.id] = _convert_team_to_model(team, session)

            # Add teams with shared integrations
            for team in teams_with_shared:
                if team.id not in all_teams:
                    all_teams[team.id] = _convert_team_to_model(team, session)

            team_models = list(all_teams.values())

            result = IntegrationTeamsModel(
                integration_type=integration_type,
                teams=team_models,
            )

            logger.info(f"Found {len(team_models)} teams with {integration_type} integrations")
            return result

        except Exception as e:
            logger.error(f"Error listing integration teams: {str(e)}")
            raise UserValidationError(f"Failed to list integration teams: {str(e)}")


def share_integration_with_team_service(
        integration_id: str,
        team_id: str,
        session: Optional[Session] = None
):
    """
    Share integration with a team.
    :param integration_id: Integration id.
    :param team_id: Team id.
    :param session: Client session if any.
    :return: Operation result.
    """
    with get_db_session(session) as session:
        try:
            # Verify integration exists and is active
            integration = (
                session.query(GithubInstallation)
                .filter(
                    and_(
                        GithubInstallation.installation_id == integration_id,
                        GithubInstallation.is_deleted == False,
                        GithubInstallation.status.in_(['ACTIVE', 'PENDING'])
                    )
                )
                .first()
            )

            if not integration:
                logger.error(f"Integration not found: {integration_id}")
                raise ResourceNotFound(f"Integration not found")

            # Verify team exists
            team = session.query(Team).filter(
                and_(
                    Team.id == team_id,
                    Team.is_deleted == False
                )
            ).first()

            if not team:
                logger.error(f"Team not found: {team_id}")
                raise TeamNotFoundError(team_id)

            # Check if access grant already exists
            existing_grant = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.entity_id == team_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .first()
            )

            if existing_grant:
                logger.warning(f"Integration {integration_id} already shared with team {team_id}")
                return {"status": "success", "message": "Integration already shared with team"}

            # Create new access grant
            access_grant = GitHubInstallationAccess(
                integration_id=integration_id,
                entity_id=team_id,
                granted_at=datetime.now(timezone.utc)
            )
            session.add(access_grant)

            # Mark integration as shared
            integration.is_shared = True

            session.commit()

            logger.info(f"Successfully shared integration {integration_id} with team {team_id}")
            return {"status": "success", "message": "Integration shared with team successfully"}

        except Exception as e:
            logger.error(f"Error sharing integration with team: {str(e)}")
            session.rollback()
            if isinstance(e, (TeamNotFoundError, ResourceNotFound)):
                raise
            raise DatabaseUpdateError(f"Failed to share integration with team: {str(e)}")


def unshare_integration_from_team_service(
        integration_id: str,
        team_id: str,
        session: Optional[Session] = None
):
    """
    Unshare integration from a team.
    :param integration_id: Integration id.
    :param team_id: Team id.
    :param session: Client session if any.
    :return: Operation result.
    """
    with get_db_session(session) as session:
        try:
            # Find the access grant
            access_grant = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.entity_id == team_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .first()
            )

            if not access_grant:
                logger.warning(f"No access grant found for integration {integration_id} and team {team_id}")
                return {"status": "success", "message": "Integration not shared with team"}

            # Soft delete the access grant
            access_grant.is_deleted = True
            access_grant.deleted_at = datetime.now(timezone.utc)

            # Check if integration still has other access grants
            remaining_grants = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .count()
            )

            # If no remaining grants, mark integration as not shared
            if remaining_grants == 0:
                integration = session.query(GithubInstallation).filter(
                    GithubInstallation.id == integration_id
                ).first()
                if integration:
                    integration.is_shared = False

            session.commit()

            logger.info(f"Successfully unshared integration {integration_id} from team {team_id}")
            return {"status": "success", "message": "Integration unshared from team successfully"}

        except Exception as e:
            logger.error(f"Error unsharing integration from team: {str(e)}")
            session.rollback()
            raise DatabaseUpdateError(f"Failed to unshare integration from team: {str(e)}")


def share_integration_with_company_service(
        integration_id: str,
        company_id: str,
        session: Optional[Session] = None
):
    """
    Share integration with company.
    :param integration_id: Integration id.
    :param company_id: Company id.
    :param session: Client session if any.
    :return: Operation result.
    """
    with get_db_session(session) as session:
        try:
            # Verify integration exists and is active
            integration = (
                session.query(GithubInstallation)
                .filter(
                    and_(
                        GithubInstallation.installation_id == integration_id,
                        GithubInstallation.is_deleted == False
                    )
                )
                .first()
            )

            if not integration:
                logger.error(f"Integration not found: {integration_id}")
                raise ResourceNotFound(f"Integration not found")

            # Verify company exists
            company = session.query(Company).filter(Company.id == company_id).first()
            if not company:
                logger.error(f"Company not found: {company_id}")
                raise UserValidationError(f"Company not found")

            # Check if access grant already exists for company
            existing_grant = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.entity_id == company_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .first()
            )

            if existing_grant:
                logger.warning(f"Integration {integration_id} already shared with company {company_id}")
                return {"status": "success", "message": "Integration already shared with company"}

            # Create new company-level access grant
            access_grant = GitHubInstallationAccess(
                integration_id=integration_id,
                entity_id=company_id,
                granted_at=datetime.now(timezone.utc)
            )
            session.add(access_grant)

            # Mark integration as shared
            integration.is_shared = True

            session.commit()

            logger.info(f"Successfully shared integration {integration_id} with company {company_id}")
            return {"status": "success", "message": "Integration shared with company successfully"}

        except Exception as e:
            logger.error(f"Error sharing integration with company: {str(e)}")
            session.rollback()
            if isinstance(e, (ResourceNotFound, UserValidationError)):
                raise
            raise DatabaseUpdateError(f"Failed to share integration with company: {str(e)}")


def unshare_integration_from_company_service(
        integration_id: str,
        company_id: str,
        session: Optional[Session] = None
):
    """
    Unshare integration from company.
    :param integration_id: Integration id.
    :param company_id: Company id.
    :param session: Client session if any.
    :return: Operation result.
    """
    with get_db_session(session) as session:
        try:
            # Find the company-level access grant
            access_grant = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.entity_id == company_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .first()
            )

            if not access_grant:
                logger.warning(
                    f"No company access grant found for integration {integration_id} and company {company_id}")
                return {"status": "success", "message": "Integration not shared with company"}

            # Soft delete the access grant
            access_grant.is_deleted = True
            access_grant.deleted_at = datetime.now(timezone.utc)

            # Check if integration still has other access grants
            remaining_grants = (
                session.query(GitHubInstallationAccess)
                .filter(
                    and_(
                        GitHubInstallationAccess.integration_id == integration_id,
                        GitHubInstallationAccess.is_deleted == False
                    )
                )
                .count()
            )

            # If no remaining grants, mark integration as not shared
            if remaining_grants == 0:
                integration = session.query(GithubInstallation).filter(
                    GithubInstallation.id == integration_id
                ).first()
                if integration:
                    integration.is_shared = False

            session.commit()

            logger.info(f"Successfully unshared integration {integration_id} from company {company_id}")
            return {"status": "success", "message": "Integration unshared from company successfully"}

        except Exception as e:
            logger.error(f"Error unsharing integration from company: {str(e)}")
            session.rollback()
            raise DatabaseUpdateError(f"Failed to unshare integration from company: {str(e)}")


def list_user_integrations_service(
        integration_type: IntegrationType,
        user_id: str,
        session: Optional[Session] = None
) -> IntegrationListModel:
    """
    List user integrations
    :param integration_type: Integration type.
    :param user_id: User ID to get integrations for.
    :param session: Client session if any.
    :return: IntegrationListModel object.
    """
    integration_type = integration_type.value  # map to string value
    with get_db_session(session) as session:
        try:
            # Get user's own integrations
            user_integrations = (
                session.query(GithubInstallation)
                .filter(
                    and_(
                        GithubInstallation.user_id == user_id,
                        GithubInstallation.svc_type == integration_type,
                        GithubInstallation.is_deleted == False,
                        GithubInstallation.status.in_(['ACTIVE', 'PENDING'])
                    )
                )
                .all()
            )

            # Get user's company and teams to find shared integrations
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                raise UserValidationError("User not found")

            shared_integrations = []

            # Get integrations shared with user's teams
            if user.company_id:
                user_teams = (
                    session.query(Team)
                    .join(TeamMember, Team.id == TeamMember.team_id)
                    .filter(
                        and_(
                            TeamMember.user_id == user_id,
                            TeamMember.is_deleted == False,
                            Team.is_deleted == False
                        )
                    )
                    .all()
                )

                team_ids = [team.id for team in user_teams]

                if team_ids:
                    # Get integrations shared with user's teams
                    team_shared = (
                        session.query(GithubInstallation)
                        .join(
                            GitHubInstallationAccess,
                            and_(
                                GitHubInstallationAccess.integration_id == GithubInstallation.id,
                                GitHubInstallationAccess.entity_id.in_(team_ids),
                                GitHubInstallationAccess.is_deleted == False
                            )
                        )
                        .filter(
                            and_(
                                GithubInstallation.svc_type == integration_type,
                                GithubInstallation.is_deleted == False,
                                GithubInstallation.user_id != user_id,  # Exclude user's own integrations
                                GithubInstallation.status.in_(['ACTIVE', 'PENDING'])
                            )
                        )
                        .all()
                    )
                    shared_integrations.extend(team_shared)

                # Get integrations shared with user's company
                company_shared = (
                    session.query(GithubInstallation)
                    .join(
                        GitHubInstallationAccess,
                        and_(
                            GitHubInstallationAccess.integration_id == GithubInstallation.id,
                            GitHubInstallationAccess.entity_id == user.company_id,
                            GitHubInstallationAccess.is_deleted == False
                        )
                    )
                    .filter(
                        and_(
                            GithubInstallation.svc_type == integration_type,
                            GithubInstallation.is_deleted == False,
                            GithubInstallation.user_id != user_id  # Exclude user's own integrations
                        )
                    )
                    .all()
                )
                shared_integrations.extend(company_shared)

            # Combine and deduplicate integrations
            all_integrations = {}

            # Add user's own integrations
            for integration in user_integrations:
                all_integrations[integration.id] = _convert_integration_to_model(integration, session)

            # Add shared integrations
            for integration in shared_integrations:
                if integration.id not in all_integrations:
                    all_integrations[integration.id] = _convert_integration_to_model(integration, session)

            integration_models = list(all_integrations.values())

            result = IntegrationListModel(
                integrations=integration_models,
                total_count=len(integration_models),
            )

            logger.info(f"Found {len(integration_models)} {integration_type} integrations for user {user_id}")
            return result

        except Exception as e:
            logger.error(f"Error listing user integrations: {str(e)}")
            if isinstance(e, UserValidationError):
                raise
            raise UserValidationError(f"Failed to list user integrations: {str(e)}")


def _convert_team_to_model(team: Team, session: Session) -> TeamModel:
    """
    Convert Team database model to TeamModel API model.
    :param team: Team database object.
    :param session: Database session.
    :return: TeamModel object.
    """
    # Get member count
    member_count = (
        session.query(func.count(TeamMember.id))
        .filter(
            and_(
                TeamMember.team_id == team.id,
                TeamMember.is_deleted == False
            )
        )
        .scalar()
    )

    return TeamModel(
        id=team.id,
        name=team.name,
        company_id=team.company_id,
        is_default=team.is_default,
        member_count=member_count,
        created_at=team.created_at
    )


def _convert_integration_to_model(integration: GithubInstallation, session: Session) -> IntegrationModel:
    """
    Convert GithubInstallation database model to IntegrationModel API model.
    :param integration: GithubInstallation database object.
    :param session: Database session.
    :return: IntegrationModel object.
    """
    # Get owner information
    owner = session.query(User).filter(User.id == integration.user_id).first()

    return IntegrationModel(
        id=integration.id,
        target_name=integration.target_name,
        owner_id=owner.id if owner else None,
        type=integration.svc_type.value
    )


def get_integration_details_service(
        integration_id: str,
        session: Optional[Session] = None
) -> IntegrationModel:
    """
    Get detailed information about a specific integration.
    :param integration_id: Integration ID.
    :param session: Client session if any.
    :return: IntegrationModel object with detailed information.
    """
    with get_db_session(session) as session:
        try:
            integration = (
                session.query(GithubInstallation)
                .options(joinedload(GithubInstallation.user))
                .filter(
                    and_(
                        GithubInstallation.id == integration_id,
                        GithubInstallation.is_deleted == False
                    )
                )
                .first()
            )

            if not integration:
                logger.error(f"Integration not found: {integration_id}")
                raise ResourceNotFound("Integration not found")

            result = _convert_integration_to_model(integration, session)

            logger.info(f"Retrieved integration details for {integration_id}")
            return result

        except Exception as e:
            logger.error(f"Error getting integration details: {str(e)}")
            if isinstance(e, ResourceNotFound):
                raise
            raise UserValidationError(f"Failed to get integration details: {str(e)}")