# Filenames
import os
from enum import Enum


GITHUB_REPO_PREFIX = "GitHub Repo"
PROMPT_FILE_NAME = "Input Prompt"
DOCUMENT_PROMPT_FILE_NAME = "Document Prompt"
PRD_NAME = "Product Requirements Document (PRD)"
CODE_STRUCTURE_NAME = "Code Structure"
DOWNLOADED_CODE_STRUCTURE_NAME = "Code Structure - downloaded"
TECH_SPECIFICATION_NAME = "Technical Specifications"
REPO_STRUCTURE_NAME = F"{GITHUB_REPO_PREFIX} Files List"
REPO_MAPPING_NAME = F"{GITHUB_REPO_PREFIX} - mapping"
DEPENDENCY_MANIFEST_NAME = f"{CODE_STRUCTURE_NAME} - manifest"
COMPILER_OUTPUT_BASE = f"{CODE_STRUCTURE_NAME} - compiler output"
PROJECT_GUIDE_NAME = "Project Guide"
CODE_IMPLEMENTATION_NAME = f"{CODE_STRUCTURE_NAME} - implementation"
CODE_THINKING_NAME = f"{CODE_STRUCTURE_NAME} - thinking"
REPO_METADATA_NAME = f"{GITHUB_REPO_PREFIX} - metadata"
BLITZY_FOLDER_PATH = "blitzy"
DOCUMENTATION_FOLDER_PATH = f"{BLITZY_FOLDER_PATH}/documentation"
PLATFORM_EVENTS_TOPIC = "platform-events"
"""Platform event process topic name."""
PROMPT_FILE_NAME_ENCODED = "Input%20Prompt"
PRD_NAME_ENCODED = "Product%20Requirements%20Document%20(PRD)"
TECH_SPECIFICATION_NAME_ENCODED = "Technical%20Specifications"
# Config
DEFAULT_MAX_RETRIES = 12
DEFAULT_SLEEP_TIME = 60
CODE_ONBOARDING_BATCH_SIZE = 250
DEFAULT_MIN_WAIT = 15  # Start with 15 second wait
DEFAULT_MAX_WAIT = 30 * 2**12  # Maximum wait
DEFAULT_MULTIPLIER = 2  # Exponential multiplier (doubling)

DOWNLOAD_CODE_TOPIC = "download-code"
"""A topic for downloading code from GitHub."""

GENERATE_REVERSE_DOCUMENT_TOPIC = "generate-reverse-document"
"""A topic for reverse document generation."""

GENERATE_REVERSE_FILE_MAP_TOPIC = "generate-reverse-file-map"
"""A topic for reverse file map generation."""

DOCUMENT_MODE_UPDATE = "UPDATE"

DEFAULT_NAME = "default"

DEPENDENCY_FILES = [
    'package.json',
    'requirements.txt',
    'Gemfile',
    'pom.xml',
    'build.gradle',
    'composer.json',
    'Podfile',
    'Package.swift'
]

GITHUB_CREATE_REPO_DESCRIPTION = "Created by Blitzy"
"""Create repository description."""

# Variables names we use to pass tracking data to platform
CORRELATION_ID_VAR_NAME = "CORRELATION_ID"
REQUEST_ID_VAR_NAME = "REQUEST_ID"


def get_project_id():
    """Retrieve PROJECT_ID, ensure its presence, or return a sensible default."""
    project_id = os.getenv("PROJECT_ID")
    if not project_id:  # Option to raise an error if it's missing
        raise RuntimeError("PROJECT_ID environment variable is required but not set.")
    return project_id


class SvcType(Enum):
    GITHUB = 'GITHUB'
    AZURE_DEVOPS = 'AZURE_DEVOPS'
