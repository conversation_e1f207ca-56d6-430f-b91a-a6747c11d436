const TECH_SPEC_DATA = {
    document_title: 'Technical Specifications',
    sections: [
        {
            title: '1. Introduction',
            content: '# 1. Introduction\n\n',
            editableContent:
                '## 1.1 Executive Summary\n\nThis project involves the development of a Node.js tutorial application that demonstrates fundamental web server capabilities through a simple HTTP endpoint implementation. The application serves as an educational resource for developers learning Node.js web development fundamentals, featuring a single `/hello` endpoint that returns a "Hello world" response to HTTP clients.\n\n## 1.2 System Overview\n\nThis is a **technical specification** example. <span style="background-color: rgba(91, 57, 243, 0.2)">diff</span>\n\n## 1.3 Basic Features\n\n- Item 1\n- Item 2\n- Item 3',
            level: 'H1',
            groupKey: '1',
            subsections: [
                {
                    title: '## 1.1 EXECUTIVE SUMMARY',
                    heading_level: 2,
                },
                {
                    title: '## 1.2 SYSTEM OVERVIEW',
                    heading_level: 2,
                },
                {
                    title: '## 1.3 BASIC FEATURES',
                    heading_level: 2,
                },
            ],
        },
        {
            title: '2. Product Requirements',
            content: '# 2. Product Requirements\n\n',
            editableContent:
                '## 2.1 Architecture Overview\n\nA very simple diagram showing the system flow:\n\n```mermaid\ngraph TD\n    A[Start] --> B{Is it raining?}\n    B -->|Yes| C[Bring an umbrella]\n    B -->|No| D[Enjoy the sunshine]\n    C --> E[End]\n    D --> E[End]\n```',
            level: 'H1',
            groupKey: '2',
            subsections: [
                {
                    title: '## 2.1 ARCHITECTURE OVERVIEW',
                    heading_level: 2,
                },
            ],
        },
        {
            title: '3. Technology Stack',
            content: '# 3. Technology Stack\n\n',
            editableContent:
                '## 3.1 Flow Diagram\n\nA diagram that fails mermaid validator due to special character in the node label but actually renders perfectly:\n\n```mermaid\nflowchart LR\n    subgraph "Notification Flow"\n        E[Event] --> Q[Queue]\n        Q --> P[Priority Sort]\n        P --> D[Delivery]\n        \n        D --> |High| H[< 30s]\n        D --> |Medium| M[< 5m]\n        D --> |Low| L[< 15m]\n    end\n```',
            level: 'H1',
            groupKey: '3',
            subsections: [
                {
                    title: '## 3.1 FLOW DIAGRAM',
                    heading_level: 2,
                },
            ],
        },
    ],
};