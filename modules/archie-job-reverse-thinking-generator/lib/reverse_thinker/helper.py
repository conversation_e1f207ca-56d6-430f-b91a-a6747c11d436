import json
from typing import Dict, Any, Literal, List

from blitzy_utils.common import download_text_file_from_gcs_using_admin_service, upload_text_to_gcs_using_admin_service
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.prebuilt import ToolNode
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from anthropic import BadRequestError as AnthropicBadRequestError
from google.cloud.storage import Client as StorageClient

from blitzy_utils.consts import DEFAULT_MAX_RETRIES
from blitzy_utils.logger import logger
from blitzy_utils.enums import Backprop<PERSON>ommand

from blitzy_platform_shared.common.utils import get_formatted_tool_result_messages, archie_exponential_retry, clean_path, \
    format_messages, make_llm_request, process_tool_call, process_messages_with_tool_call
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION, get_file_contents
from blitzy_platform_shared.code_graph.builder import CodeGraph<PERSON>uilder
from blitzy_platform_shared.code_graph.tools import get_folder_contents
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import SUMMARY_OF_CHANGES_INPUT, TECH_SPEC_SECTIONS_INPUT
from blitzy_platform_shared.code_generation.models import FileOrFolderStatus, FileSchemaType, ThinkingExternalImport
from blitzy_platform_shared.code_generation.utils import create_dependency_sorted_list, get_property, rebuild_files_map
from blitzy_platform_shared.code_generation.tools import get_file_schema, get_internal_imports, add_or_update_internal_import, \
    remove_internal_import, get_external_imports, add_or_update_external_import, remove_external_import, get_exports, \
    add_or_update_export, remove_export, mark_file_complete, add_created_file, FILE_COMPLETE_TOOL_NAME, \
    download_file_schemas, upload_file_schemas, update_unchanged_file

from .state import ReverseThinkerState, get_state
from .prompts import CREATE_FILE_SYSTEM_PROMPT_TEMPLATE, AGENT_PERSONA_PROMPT, CREATE_FILE_INPUTS, COMMON_RULES_PROMPTLET, CREATE_RULES_PROMPTLET, \
    ASSIGNED_FILE_PATH_INPUT, FILE_INFORMATION_INPUT, SOURCE_FILES_INPUT, FILE_DEPENDS_ON_INPUT, FILE_UPDATE_INFORMATION_INPUT, UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE, \
    UPDATE_FILE_INPUTS, UPDATE_RULES_PROMPTLET

tool_node_tools = [
    get_tech_spec_section,
    get_file_schema,
    get_internal_imports,
    add_or_update_internal_import,
    remove_internal_import,
    get_external_imports,
    add_or_update_external_import,
    remove_external_import,
    get_exports,
    add_or_update_export,
    remove_export,
    mark_file_complete,
    add_created_file,
    update_unchanged_file,
    get_file_contents,
    get_folder_contents
]
rt_tools = tool_node_tools + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]


class ReverseThinkerHelper:
    def __init__(
        self,
        generator_llm: BaseChatModel,
        fallback_llms: List[BaseChatModel],
        job_metadata: Dict[str, Any],
        blob_name: str,
        bucket_name: str,
        compressed_files_map: Dict[str, List[Dict[str, Any]]],
        head_commit_hash: str,
        github_server: str,
        state_metadata_filename: str,
        file_schemas_filename: str,
        repo_map_filename: str,
        graph_builder: CodeGraphBuilder,
        tech_spec_parsed: Dict[str, str]
    ):
        self.graph_builder = graph_builder
        self.generator_llm = generator_llm
        self.fallback_llms = fallback_llms
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.job_metadata = job_metadata
        self.head_commit_hash = head_commit_hash
        self.github_server = github_server
        self.state_metadata_filename = state_metadata_filename
        self.file_schemas_filepath = f"{self.blob_name}/{file_schemas_filename}"
        self.repo_map_filename = repo_map_filename
        self.tech_spec_parsed = tech_spec_parsed

        self.company_id = self.job_metadata["company_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_id = self.job_metadata["branch_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.create_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.git_project_repo_id = self.job_metadata["git_project_repo_id"]
        self.dest_repo_name = self.repo_name
        if self.create_dest_repo:
            self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.base_branch_name = self.job_metadata["branch_name"]
        self.job_type = self.job_metadata["job_type"]

        self.compressed_files_map = compressed_files_map.copy()
        repo_files_filepath = f"{self.blob_name}/{self.repo_map_filename}"
        self.files_map = json.loads(
            download_text_file_from_gcs_using_admin_service(
                file_path=repo_files_filepath,
                company_id=self.company_id,
            )
        )
        self.all_file_paths = []
        for file_list in self.compressed_files_map.values():
            self.all_file_paths.extend(file_list)

    def create_graph(self) -> StateGraph:
        # Define the graph
        rt_generator = StateGraph(ReverseThinkerState)

        # Add nodes
        rt_generator.add_node("setup", self.setup)
        rt_generator.add_node("setup_file", self.setup_file)
        rt_generator.add_node("created_file", self.process_created_file)
        rt_generator.add_node("updated_file", self.process_updated_file)
        rt_generator.add_node("deleted_file", self.process_deleted_file)
        rt_generator.add_node("update_state", self.update_state)
        rt_generator.add_node("teardown", self.teardown)

        rt_generator.add_conditional_edges(
            "setup",
            self.file_router,
            {
                "continue": "setup_file",
                "end": "teardown"
            }
        )

        rt_generator.add_conditional_edges(
            "setup_file",
            self.setup_router,
            {
                "created": "created_file",
                "updated": "updated_file",
                "deleted": "deleted_file",
            }
        )

        rt_generator.add_conditional_edges(
            "update_state",
            self.file_router,
            {
                "continue": "setup_file",
                "end": "teardown"
            }
        )

        # Set the entry point
        rt_generator.add_edge(START, "setup")
        rt_generator.add_edge("created_file", "update_state")
        rt_generator.add_edge("updated_file", "update_state")
        rt_generator.add_edge("deleted_file", "update_state")
        rt_generator.add_edge("teardown", END)
        return rt_generator

    def setup_router(self, state: ReverseThinkerState) -> Literal["created", "updated", "deleted"]:
        if state["current_file_info"]["status"] == "CREATED":
            return "created"
        elif state["current_file_info"]["status"] == "DELETED":
            return "deleted"
        else:
            return "updated"

    def file_router(self, state: ReverseThinkerState) -> Literal["continue", "end"]:
        if state["file_index"] >= len(self.all_file_paths) or self.job_type == BackpropCommand.DOCUMENT_CODE.value:
            return "end"
        return "continue"

    @archie_exponential_retry()
    def setup(self, state: ReverseThinkerState) -> Dict[str, Any]:
        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                download_file_path = f"{self.blob_name}/{self.state_metadata_filename}"
                state_metadata: Dict[str, str] = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=download_file_path,
                    company_id=self.company_id,
                ))

                state["file_index"] = state_metadata["file_index"]
                state["current_file_info"] = state_metadata["current_file_info"]
                state["json_retry_count"] = state_metadata["json_retry_count"]
                state["retry_count"] = state_metadata["retry_count"]
                state["sorted_files_list"] = state_metadata["sorted_files_list"]
                state["dependency_map"] = state_metadata["dependency_map"]
            except Exception as e:
                logger.warning(f'Failed to resume, running a fresh operation')
                state["resume"] = False
                return self.setup(state=state)
        else:
            file_schemas = {}
            for file_list in self.files_map.values():
                for file in file_list:
                    dest_path = clean_path(file["dest_path"])
                    if file["status"] == FileOrFolderStatus.UPDATED:
                        file["new_internal_imports"] = []
                        file["new_external_imports"] = []
                        file["new_exports"] = []
                    else:
                        file["internal_imports"] = []
                        file["external_imports"] = []
                        file["exports"] = []
                    file_schemas[dest_path] = file

            # Build file schemas for the first time
            upload_file_schemas(
                file_path=self.file_schemas_filepath,
                company_id=self.company_id,
                file_schemas=file_schemas
            )

            state["current_file_info"] = {}
            state["json_retry_count"] = 0
            state["retry_count"] = 0
            state["sorted_files_list"] = []
            state["file_index"] = 0
            state["dependency_map"] = {}

        return get_state(state=state)

    @archie_exponential_retry()
    def setup_file(self, state: ReverseThinkerState) -> Dict[str, Any]:
        file_schemas = download_file_schemas(
            file_path=self.file_schemas_filepath,
            company_id=self.company_id
        )
        file_path = self.all_file_paths[state["file_index"]]
        state["current_file_info"] = file_schemas[file_path]

        return get_state(state=state)

    @archie_exponential_retry()
    def process_created_file(self, state: ReverseThinkerState) -> Dict[str, Any]:
        file_path = clean_path(state["current_file_info"]["dest_path"])
        logger.info(f"processing created file: {file_path}")

        file_summary = state["current_file_info"]["summary"]
        requirements = state["current_file_info"]["requirements"]
        key_changes = state["current_file_info"]["key_changes"]
        depends_on_files = state["current_file_info"]["depends_on_files"]
        source_files = state["current_file_info"]["source_files"]

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": CREATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=AGENT_PERSONA_PROMPT,
                        inputs=CREATE_FILE_INPUTS,
                        rules=f"{COMMON_RULES_PROMPTLET}\n\n{CREATE_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": SUMMARY_OF_CHANGES_INPUT.format(
                            summary=state["summary_of_changes"]
                        )
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(self.tech_spec_parsed.keys()))
                        )
                    },
                    {
                        "type": "text",
                        "text": ASSIGNED_FILE_PATH_INPUT.format(
                            path=file_path
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_INFORMATION_INPUT.format(
                            summary=file_summary,
                            requirements=requirements,
                            key_changes=key_changes,
                            depends_on_files=depends_on_files
                        )
                    },
                    {
                        "type": "text",
                        "text": SOURCE_FILES_INPUT.format(
                            source_files=source_files
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_DEPENDS_ON_INPUT.format(
                            depends_on_files=depends_on_files
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]
            )
        ]

        self.process_file(
            state=state,
            messages=messages,
            file_path=file_path
        )

        return get_state(state=state)

    @archie_exponential_retry()
    def process_updated_file(self, state: ReverseThinkerState) -> Dict[str, Any]:
        file_path = clean_path(state["current_file_info"]["dest_path"])
        logger.info(f"processing updated file: {file_path}")

        key_changes = state["current_file_info"]["key_changes"]
        is_dependency_file = state["current_file_info"]["is_dependency_file"]
        depends_on_files = state["current_file_info"]["depends_on_files"]

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=AGENT_PERSONA_PROMPT,
                        inputs=UPDATE_FILE_INPUTS,
                        rules=f"{COMMON_RULES_PROMPTLET}\n\n{UPDATE_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": SUMMARY_OF_CHANGES_INPUT.format(
                            summary=state["summary_of_changes"]
                        )
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(list(self.tech_spec_parsed.keys()))
                        )
                    },
                    {
                        "type": "text",
                        "text": ASSIGNED_FILE_PATH_INPUT.format(
                            path=file_path
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_UPDATE_INFORMATION_INPUT.format(
                            changes=key_changes,
                            is_dependency_file=is_dependency_file
                        )
                    },
                    {
                        "type": "text",
                        "text": FILE_DEPENDS_ON_INPUT.format(
                            depends_on_files=depends_on_files
                        ),
                        "cache_control": {"type": "ephemeral"}
                    }
                ]
            )
        ]

        self.process_file(
            state=state,
            messages=messages,
            file_path=file_path
        )

        return get_state(state=state)

    @archie_exponential_retry()
    def process_file(
        self,
        state: ReverseThinkerState,
        messages: List[BaseMessage],
        file_path: str
    ) -> Dict[str, Any]:
        orig_messages = messages.copy()
        llm = self.generator_llm
        while True:
            messages = orig_messages.copy()
            try:
                file_processed = False
                response: AIMessage = make_llm_request(
                    llm=llm,
                    messages=messages
                )
                tool_calls = response.tool_calls
                while len(tool_calls):
                    messages.append(response)
                    total_tokens = response.usage_metadata["total_tokens"]
                    tools_config = {
                        "file_schemas_filepath": self.file_schemas_filepath,
                        "git_project_repo_id": self.git_project_repo_id,
                        "tech_spec_parsed": self.tech_spec_parsed,
                        "repo_name": self.repo_name,
                        "branch_name": self.base_branch_name,
                        "user_id": self.user_id,
                        "head_commit_hash": self.head_commit_hash,
                        "github_server": self.github_server,
                        "company_id": self.company_id,
                        "repo_id": self.repo_id,
                        "branch_id": self.branch_id,
                        "graph_builder": self.graph_builder,
                        "is_new_dest_repo": self.create_dest_repo,
                        "file_mapping": self.files_map
                    }
                    for tool_call in tool_calls:
                        tool_name = tool_call["name"]
                        if tool_name in [FILE_COMPLETE_TOOL_NAME]:
                            file_processed = True
                        tool_message = process_tool_call(
                            tool_call=tool_call,
                            tools_list=tool_node_tools,
                            tools_config=tools_config
                        )
                        messages = process_messages_with_tool_call(
                            tool_message=tool_message,
                            messages=messages,
                            total_tokens_before_tool=total_tokens,
                            llm=llm
                        )
                    # logger.info(f'sending tool response back to llm for file: {file_path}')
                    response: AIMessage = make_llm_request(
                        llm=llm,
                        messages=messages
                    )
                    tool_calls = response.tool_calls

                if not file_processed:
                    if state["json_retry_count"] < DEFAULT_MAX_RETRIES:
                        logger.warning('file not marked completed, retrying')
                        state["json_retry_count"] += 1
                    else:
                        logger.error(
                            f'exhaused all retries due to missing output, skipping file: {file_path}')
                        state["retry_count"] = 0
                        state["json_retry_count"] = 0
                        break
                else:
                    state["retry_count"] = 0
                    state["json_retry_count"] = 0
                    break
            except AnthropicBadRequestError as e:
                logger.warning(
                    f'Anthropic bad request error, trying with fallback index {state["retry_count"]}: {e}')
                if state["retry_count"] < len(self.fallback_llms):
                    llm = self.fallback_llms[state["retry_count"]]
                    state["retry_count"] += 1
                else:
                    logger.error(f'Anthropic bad request error, all fallback llms failed: {e}')
                    file_processed = True
                    state["retry_count"] = 0
                    state["json_retry_count"] = 0
                    break

        logger.info(f"finished processing file: {file_path}")

        return get_state(state=state)

    @archie_exponential_retry()
    def process_deleted_file(self, state: ReverseThinkerState) -> Dict[str, Any]:
        logger.info(f'Ignoring deleted file: {state["current_file_info"]["dest_path"]}')

        return get_state(state=state)

    @archie_exponential_retry()
    def update_state(self, state: ReverseThinkerState) -> Dict[str, Any]:
        state["file_index"] += 1
        self.upload_state(state=state)

        file_schemas = download_file_schemas(
            file_path=self.file_schemas_filepath,
            company_id=self.company_id
        )
        self.files_map = rebuild_files_map(file_schemas=file_schemas)
        destination_blob_name = f"{self.blob_name}/{self.repo_map_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_blob_name,
            company_id=self.company_id,
            data=json.dumps(self.files_map)
        )

        return get_state(state=state)

    @archie_exponential_retry()
    def upload_state(self, state: ReverseThinkerState) -> Dict[str, Any]:
        state_metadata = get_state(state=state)
        destination_blob_name = f"{self.blob_name}/{self.state_metadata_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_blob_name,
            company_id=self.company_id,
            data=json.dumps(state_metadata)
        )
        return state_metadata

    @archie_exponential_retry()
    def teardown(self, state: ReverseThinkerState) -> Dict[str, Any]:
        file_schemas: FileSchemaType = download_file_schemas(
            file_path=self.file_schemas_filepath,
            company_id=self.company_id,
        )
        if self.job_type == BackpropCommand.DOCUMENT_CODE.value:
            logger.info(f'Bypassing sorting for document code mode')
            state["sorted_files_list"] = list(file_schemas.keys())
            state["dependency_map"] = {}
        else:
            logger.info(f'Sorting files list')
            state["sorted_files_list"] = create_dependency_sorted_list(
                file_schemas=file_schemas
            )
            state["dependency_map"] = self.get_dependency_map(
                file_schemas=file_schemas
            )
        return get_state(state=state)

    def get_dependency_map(self, file_schemas: FileSchemaType) -> Dict[str, Any]:
        dependency_map = {}
        for file_path in file_schemas:
            external_imports: List[ThinkingExternalImport] = get_property(
                file_path=file_path,
                file_schemas=file_schemas,
                updated_file_property='new_external_imports',
                default_property='external_imports'
            )
            dependency_map[file_path] = {}
            for external_import in external_imports:
                package_name = external_import["package_name"]
                dependency_map[file_path][package_name] = external_import
        return dependency_map
